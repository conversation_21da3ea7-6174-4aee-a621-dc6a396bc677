{% extends 'base.html' %}

{% block title %}Record Growth for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .growth-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .growth-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .growth-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .form-row-three {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row, .form-row-three {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .measurement-input {
        position: relative;
    }
    
    .measurement-input .unit {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-500);
        font-size: var(--font-sm);
        pointer-events: none;
    }
    
    .measurement-input input {
        padding-right: 40px;
    }
    
    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xl);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .file-upload-container:hover {
        border-color: var(--primary);
        background-color: var(--gray-50);
    }
    
    .file-upload-icon {
        font-size: 2rem;
        color: var(--gray-400);
        margin-bottom: var(--spacing-md);
    }
    
    .file-upload-text {
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }
    
    .file-upload-info {
        font-size: var(--font-sm);
        color: var(--gray-500);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="growth-form-container">
        <div class="growth-form-header">
            <h1>Record Growth for {{ pet.name }}</h1>
            <p>Track your pet's physical development over time</p>
        </div>
        
        <div class="growth-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Growth Measurements</h2>
                    
                    <div class="form-group">
                        <label for="{{ form.date_recorded.id_for_label }}" class="form-label">Date Recorded</label>
                        {{ form.date_recorded }}
                    </div>
                    
                    <div class="form-row-three">
                        <div class="form-group measurement-input">
                            <label for="{{ form.weight.id_for_label }}" class="form-label">Weight (Required)</label>
                            {{ form.weight }}
                            <span class="unit">kg</span>
                        </div>
                        
                        <div class="form-group measurement-input">
                            <label for="{{ form.height.id_for_label }}" class="form-label">Height (Optional)</label>
                            {{ form.height }}
                            <span class="unit">cm</span>
                        </div>
                        
                        <div class="form-group measurement-input">
                            <label for="{{ form.length.id_for_label }}" class="form-label">Length (Optional)</label>
                            {{ form.length }}
                            <span class="unit">cm</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                        {{ form.notes }}
                        <small class="form-text">Any observations about your pet's growth or development</small>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Photo (Optional)</h2>
                    
                    <div class="file-upload-container" id="photo-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-image"></i>
                        </div>
                        <div class="file-upload-text">Click to upload a photo from this date</div>
                        <div class="file-upload-info">JPG, PNG, or GIF • Max 10MB</div>
                        <div style="display:none;">{{ form.photo }}</div>
                    </div>
                    
                    <div id="file-name-display" class="form-text"></div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Record Growth</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set today's date as default
        const dateInput = document.querySelector('input[name="date_recorded"]');
        if (dateInput && !dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
        
        // File upload handling
        const photoUploadContainer = document.getElementById('photo-upload-container');
        const photoInput = document.querySelector('input[name="photo"]');
        const fileNameDisplay = document.getElementById('file-name-display');
        
        if (photoUploadContainer && photoInput) {
            photoUploadContainer.addEventListener('click', function() {
                photoInput.click();
            });
            
            photoInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileName = this.files[0].name;
                    fileNameDisplay.textContent = `Selected file: ${fileName}`;
                    photoUploadContainer.style.borderColor = 'var(--primary)';
                } else {
                    fileNameDisplay.textContent = '';
                    photoUploadContainer.style.borderColor = 'var(--gray-300)';
                }
            });
        }
    });
</script>
{% endblock %}
