{% extends 'base.html' %}
{% load static %}

{% block title %}Add Availability - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="form-header">
        <a href="{% url 'provider-availability' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Availability
        </a>
        <h1>Add Availability Slot</h1>
        <p>Set your available times for providing services</p>
    </div>

    <div class="form-container">
        <div class="form-card">
            <form method="post" class="availability-form">
                {% csrf_token %}
                
                <div class="form-group">
                    {{ form.day_of_week.label_tag }}
                    {{ form.day_of_week }}
                    {% if form.day_of_week.errors %}
                        <div class="form-errors">
                            {{ form.day_of_week.errors }}
                        </div>
                    {% endif %}
                </div>

                <div class="time-group">
                    <div class="form-group">
                        {{ form.start_time.label_tag }}
                        {{ form.start_time }}
                        {% if form.start_time.errors %}
                            <div class="form-errors">
                                {{ form.start_time.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.end_time.label_tag }}
                        {{ form.end_time }}
                        {% if form.end_time.errors %}
                            <div class="form-errors">
                                {{ form.end_time.errors }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-actions">
                    <a href="{% url 'provider-availability' %}" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Availability
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.form-header h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.form-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
    margin: 0;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
}

.form-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
}

.availability-form {
    display: grid;
    gap: var(--spacing-lg);
}

.form-group {
    display: grid;
    gap: var(--spacing-xs);
}

.time-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-base);
}

.form-group label {
    color: var(--gray-700);
    font-weight: 500;
    font-size: var(--font-sm);
}

.form-group input,
.form-group select {
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-errors {
    color: var(--danger);
    font-size: var(--font-sm);
}

.form-actions {
    display: flex;
    gap: var(--spacing-r);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
    .time-group {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
{% endblock %}
