{% extends 'base.html' %}
{% load static %}

{% block title %}Book Service - {{ provider.user.full_name|default:provider.user.email }} | PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="booking-header">
        <a href="{% url 'provider-detail' provider.pk %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Provider
        </a>
        <h1>Select a Service</h1>
        <p>Choose which service you'd like to book with {{ provider.user.full_name|default:provider.user.email }}</p>
    </div>

    <div class="provider-summary">
        <div class="provider-card">
            <div class="provider-avatar">
                {% if provider.profile_picture %}
                    <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.full_name|default:provider.user.email }}">
                {% else %}
                    <div class="avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
            </div>
            <div class="provider-details">
                <h2>{{ provider.user.full_name|default:provider.user.email }}</h2>
                <div class="provider-rating">
                    <div class="stars">
                        {% for i in "12345" %}
                            {% if forloop.counter <= provider.rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="rating-text">{{ provider.rating|floatformat:1 }} ({{ provider.reviews_count }} reviews)</span>
                </div>
                <div class="provider-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ provider.experience_years }} years experience</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ provider.location|default:"Location not specified" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="services-selection">
        <h3>Available Services</h3>
        <form method="post" class="service-selection-form">
            {% csrf_token %}
            <div class="services-grid">
                {% for service in services %}
                    <div class="service-option">
                        <input type="radio" name="service" value="{{ service.pk }}" id="service_{{ service.pk }}" required>
                        <label for="service_{{ service.pk }}" class="service-card">
                            <div class="service-header">
                                <h4>{{ service.name }}</h4>
                                <span class="service-price">${{ service.price }}</span>
                            </div>
                            <p class="service-description">{{ service.description|truncatewords:15 }}</p>
                            <div class="service-duration">
                                <i class="fas fa-clock"></i>
                                <span>{{ service.duration }} minutes</span>
                            </div>
                        </label>
                    </div>
                {% endfor %}
            </div>
            
            <div class="form-actions">
                <a href="{% url 'provider-detail' provider.pk %}" class="btn btn-outline">Cancel</a>
                <button type="submit" class="btn btn-primary">Continue to Booking</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.booking-header {
    margin-bottom: var(--spacing-xl);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.booking-header h1 {
    color: var(--gray-800);
    margin: 0 0 var(--spacing-sm) 0;
}

.booking-header p {
    color: var(--gray-600);
    margin: 0;
}

.provider-summary {
    margin-bottom: var(--spacing-xl);
}

.provider-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.provider-avatar {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    overflow: hidden;
    background: var(--gray-200);
    flex-shrink: 0;
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: 2rem;
}

.provider-details h2 {
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-xl);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.stars {
    color: var(--warning);
}

.rating-text {
    color: var(--gray-600);
    font-size: var(--font-sm);
}

.provider-meta {
    display: flex;
    gap: var(--spacing-base);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-700);
    font-size: var(--font-sm);
}

.meta-item i {
    color: var(--primary);
    width: 16px;
}

.services-selection {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
}

.services-selection h3 {
    color: var(--gray-800);
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-xl);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.service-option {
    position: relative;
}

.service-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.service-card {
    display: block;
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-base);
    height: 100%;
}

.service-card:hover {
    border-color: var(--primary-light);
    box-shadow: var(--shadow-sm);
}

.service-option input[type="radio"]:checked + .service-card {
    border-color: var(--primary);
    background: var(--primary-light);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.service-header h4 {
    color: var(--primary);
    margin: 0;
    font-size: var(--font-lg);
}

.service-price {
    color: var(--primary);
    font-weight: 600;
    font-size: var(--font-lg);
}

.service-description {
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.service-duration {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-600);
    font-size: var(--font-sm);
}

.service-duration i {
    color: var(--primary);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-base);
}

@media (max-width: 768px) {
    .provider-card {
        flex-direction: column;
        text-align: center;
    }
    
    .provider-meta {
        justify-content: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
{% endblock %}
