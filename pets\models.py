from django.db import models
from django.urls import reverse
from users.models import User


class PetCategory(models.Model):
    """Model for pet categories (dog, cat, bird, etc.)"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to='category_icons', blank=True)

    class Meta:
        verbose_name_plural = 'Pet Categories'

    def __str__(self):
        return self.name


class PetBreed(models.Model):
    """Model for pet breeds"""
    category = models.ForeignKey(PetCategory, on_delete=models.CASCADE, related_name='breeds')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    average_lifespan = models.CharField(max_length=50, blank=True)
    size = models.CharField(max_length=50, blank=True)
    temperament = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.name} ({self.category.name})"


class Pet(models.Model):
    """Model for pets"""
    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
        ('U', 'Unknown'),
    )

    SIZE_CHOICES = (
        ('XS', 'Extra Small'),
        ('S', 'Small'),
        ('M', 'Medium'),
        ('L', 'Large'),
        ('XL', 'Extra Large'),
    )

    ACTIVITY_LEVEL_CHOICES = (
        ('low', 'Low'),
        ('moderate', 'Moderate'),
        ('high', 'High'),
        ('very_high', 'Very High'),
    )

    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pets')
    name = models.CharField(max_length=100)
    category = models.ForeignKey(PetCategory, on_delete=models.CASCADE, related_name='pets')
    breed = models.ForeignKey(PetBreed, on_delete=models.SET_NULL, null=True, blank=True, related_name='pets')
    birth_date = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='U')
    profile_picture = models.ImageField(upload_to='pet_profiles', default='default_pet.jpg')
    bio = models.TextField(blank=True)

    # Adoption settings
    is_for_adoption = models.BooleanField(default=False)
    adoption_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Enhanced pet information
    microchip_id = models.CharField(max_length=50, blank=True, help_text="Microchip identification number")
    registration_number = models.CharField(max_length=100, blank=True, help_text="Breed registration number")
    current_weight = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text="Current weight in kg")
    size = models.CharField(max_length=2, choices=SIZE_CHOICES, blank=True)
    activity_level = models.CharField(max_length=10, choices=ACTIVITY_LEVEL_CHOICES, blank=True)

    # Health information
    is_spayed_neutered = models.BooleanField(default=False)
    allergies = models.TextField(blank=True, help_text="Known allergies and sensitivities")
    special_needs = models.TextField(blank=True, help_text="Special care requirements")

    # Emergency contacts
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, blank=True)

    # Insurance and documents
    insurance_provider = models.CharField(max_length=100, blank=True)
    insurance_policy_number = models.CharField(max_length=100, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    followers = models.ManyToManyField(User, related_name='followed_pets', blank=True)

    def __str__(self):
        return f"{self.name} ({self.owner.username}'s {self.category.name})"

    def get_absolute_url(self):
        return reverse('pet-detail', kwargs={'pk': self.pk})

    def get_age(self):
        """Calculate pet's age based on birth date"""
        if not self.birth_date:
            return "Unknown"

        from datetime import date
        today = date.today()
        age = today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

        if age < 1:
            # Calculate months
            months = today.month - self.birth_date.month
            if today.day < self.birth_date.day:
                months -= 1
            if months < 0:
                months += 12
            return f"{months} months"
        else:
            return f"{age} years"


class PetGallery(models.Model):
    """Model for pet photo gallery"""
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='gallery')
    image = models.ImageField(upload_to='pet_gallery')
    caption = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = 'Pet Galleries'

    def __str__(self):
        return f"Photo of {self.pet.name}"


class PetMedicalRecord(models.Model):
    """Enhanced model for pet medical records"""
    RECORD_TYPE_CHOICES = (
        ('vaccination', 'Vaccination'),
        ('checkup', 'Regular Checkup'),
        ('treatment', 'Treatment'),
        ('surgery', 'Surgery'),
        ('emergency', 'Emergency Visit'),
        ('dental', 'Dental Care'),
        ('grooming', 'Grooming'),
        ('other', 'Other'),
    )

    URGENCY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('emergency', 'Emergency'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='medical_records')
    record_date = models.DateField()
    record_type = models.CharField(max_length=20, choices=RECORD_TYPE_CHOICES)
    title = models.CharField(max_length=200, help_text="Brief title for this record")
    description = models.TextField()
    symptoms = models.TextField(blank=True, help_text="Symptoms observed")
    diagnosis = models.TextField(blank=True, help_text="Veterinarian's diagnosis")
    treatment = models.TextField(blank=True, help_text="Treatment provided")
    medications = models.TextField(blank=True, help_text="Medications prescribed")
    follow_up_date = models.DateField(null=True, blank=True, help_text="Next appointment or follow-up date")
    urgency = models.CharField(max_length=10, choices=URGENCY_CHOICES, default='low')
    cost = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Cost of treatment")

    # Veterinarian information
    veterinarian = models.CharField(max_length=255, blank=True)
    clinic = models.CharField(max_length=255, blank=True)
    clinic_phone = models.CharField(max_length=20, blank=True)
    clinic_address = models.TextField(blank=True)

    # Documents and files
    document = models.FileField(upload_to='pet_medical_records', blank=True)
    prescription_image = models.ImageField(upload_to='pet_prescriptions', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-record_date', '-created_at']

    def __str__(self):
        return f"{self.pet.name}'s {self.get_record_type_display()} - {self.title} ({self.record_date})"


class Vaccination(models.Model):
    """Model for tracking pet vaccinations"""
    VACCINE_TYPE_CHOICES = (
        ('rabies', 'Rabies'),
        ('dhpp', 'DHPP (Distemper, Hepatitis, Parvovirus, Parainfluenza)'),
        ('bordetella', 'Bordetella (Kennel Cough)'),
        ('lyme', 'Lyme Disease'),
        ('fvrcp', 'FVRCP (Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia)'),
        ('felv', 'FeLV (Feline Leukemia)'),
        ('fiv', 'FIV (Feline Immunodeficiency Virus)'),
        ('other', 'Other'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='vaccinations')
    vaccine_type = models.CharField(max_length=20, choices=VACCINE_TYPE_CHOICES)
    vaccine_name = models.CharField(max_length=100, help_text="Brand/specific name of vaccine")
    date_administered = models.DateField()
    next_due_date = models.DateField(help_text="When the next dose is due")
    batch_number = models.CharField(max_length=50, blank=True)
    veterinarian = models.CharField(max_length=255)
    clinic = models.CharField(max_length=255)
    notes = models.TextField(blank=True)
    certificate = models.FileField(upload_to='vaccination_certificates', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date_administered']

    def __str__(self):
        return f"{self.pet.name} - {self.get_vaccine_type_display()} ({self.date_administered})"

    @property
    def is_due_soon(self):
        """Check if vaccination is due within 30 days"""
        from datetime import date, timedelta
        return self.next_due_date <= date.today() + timedelta(days=30)

    @property
    def is_overdue(self):
        """Check if vaccination is overdue"""
        from datetime import date
        return self.next_due_date < date.today()


class GrowthRecord(models.Model):
    """Model for tracking pet growth and development"""
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='growth_records')
    date_recorded = models.DateField()
    weight = models.DecimalField(max_digits=6, decimal_places=2, help_text="Weight in kg")
    height = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text="Height in cm")
    length = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text="Length in cm")
    notes = models.TextField(blank=True, help_text="Additional observations")
    photo = models.ImageField(upload_to='growth_photos', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date_recorded']
        unique_together = ('pet', 'date_recorded')

    def __str__(self):
        return f"{self.pet.name} - {self.weight}kg on {self.date_recorded}"


class Milestone(models.Model):
    """Model for tracking pet developmental milestones"""
    MILESTONE_TYPE_CHOICES = (
        ('physical', 'Physical Development'),
        ('behavioral', 'Behavioral'),
        ('training', 'Training Achievement'),
        ('health', 'Health Milestone'),
        ('social', 'Social Development'),
        ('other', 'Other'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='milestones')
    milestone_type = models.CharField(max_length=20, choices=MILESTONE_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    date_achieved = models.DateField()
    age_at_milestone = models.CharField(max_length=50, blank=True, help_text="Age when milestone was achieved")
    photo = models.ImageField(upload_to='milestone_photos', blank=True)
    video = models.FileField(upload_to='milestone_videos', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date_achieved']

    def __str__(self):
        return f"{self.pet.name} - {self.title} ({self.date_achieved})"


class FeedingSchedule(models.Model):
    """Model for managing pet feeding schedules"""
    MEAL_TYPE_CHOICES = (
        ('breakfast', 'Breakfast'),
        ('lunch', 'Lunch'),
        ('dinner', 'Dinner'),
        ('snack', 'Snack'),
        ('treat', 'Treat'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='feeding_schedules')
    meal_type = models.CharField(max_length=20, choices=MEAL_TYPE_CHOICES)
    time = models.TimeField(help_text="Scheduled feeding time")
    food_type = models.CharField(max_length=200, help_text="Type/brand of food")
    amount = models.CharField(max_length=100, help_text="Amount to feed (e.g., 1 cup, 200g)")
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['time']

    def __str__(self):
        return f"{self.pet.name} - {self.get_meal_type_display()} at {self.time}"


class FeedingLog(models.Model):
    """Model for logging actual feeding times and amounts"""
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='feeding_logs')
    date_time = models.DateTimeField()
    meal_type = models.CharField(max_length=20, choices=FeedingSchedule.MEAL_TYPE_CHOICES)
    food_type = models.CharField(max_length=200)
    amount_given = models.CharField(max_length=100)
    amount_consumed = models.CharField(max_length=100, blank=True, help_text="How much was actually eaten")
    notes = models.TextField(blank=True, help_text="Appetite, behavior, etc.")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date_time']

    def __str__(self):
        return f"{self.pet.name} - {self.get_meal_type_display()} on {self.date_time.date()}"


class ExerciseLog(models.Model):
    """Model for tracking pet exercise and activities"""
    ACTIVITY_TYPE_CHOICES = (
        ('walk', 'Walk'),
        ('run', 'Run'),
        ('play', 'Play Time'),
        ('training', 'Training Session'),
        ('swimming', 'Swimming'),
        ('hiking', 'Hiking'),
        ('fetch', 'Fetch'),
        ('agility', 'Agility Training'),
        ('other', 'Other'),
    )

    INTENSITY_CHOICES = (
        ('low', 'Low'),
        ('moderate', 'Moderate'),
        ('high', 'High'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='exercise_logs')
    date = models.DateField()
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPE_CHOICES)
    duration_minutes = models.PositiveIntegerField(help_text="Duration in minutes")
    intensity = models.CharField(max_length=10, choices=INTENSITY_CHOICES)
    distance = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text="Distance in km")
    location = models.CharField(max_length=200, blank=True)
    notes = models.TextField(blank=True)
    weather = models.CharField(max_length=100, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.pet.name} - {self.get_activity_type_display()} ({self.duration_minutes}min) on {self.date}"


class PetExpense(models.Model):
    """Model for tracking pet-related expenses"""
    EXPENSE_CATEGORY_CHOICES = (
        ('food', 'Food & Treats'),
        ('medical', 'Medical & Veterinary'),
        ('grooming', 'Grooming'),
        ('toys', 'Toys & Accessories'),
        ('training', 'Training'),
        ('boarding', 'Boarding & Pet Sitting'),
        ('insurance', 'Insurance'),
        ('supplies', 'Supplies & Equipment'),
        ('emergency', 'Emergency'),
        ('other', 'Other'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='expenses')
    date = models.DateField()
    category = models.CharField(max_length=20, choices=EXPENSE_CATEGORY_CHOICES)
    description = models.CharField(max_length=200)
    amount = models.DecimalField(max_digits=8, decimal_places=2)
    vendor = models.CharField(max_length=200, blank=True, help_text="Store, clinic, or service provider")
    receipt_image = models.ImageField(upload_to='expense_receipts', blank=True)
    notes = models.TextField(blank=True)
    is_recurring = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.pet.name} - {self.get_category_display()}: ${self.amount} ({self.date})"


class PetReminder(models.Model):
    """Model for pet care reminders and notifications"""
    REMINDER_TYPE_CHOICES = (
        ('vaccination', 'Vaccination Due'),
        ('checkup', 'Veterinary Checkup'),
        ('medication', 'Medication'),
        ('grooming', 'Grooming Appointment'),
        ('feeding', 'Feeding Time'),
        ('exercise', 'Exercise Time'),
        ('training', 'Training Session'),
        ('weight_check', 'Weight Check'),
        ('custom', 'Custom Reminder'),
    )

    FREQUENCY_CHOICES = (
        ('once', 'One Time'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='reminders')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPE_CHOICES)
    due_date = models.DateTimeField()
    frequency = models.CharField(max_length=10, choices=FREQUENCY_CHOICES, default='once')
    is_completed = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    completed_date = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['due_date']

    def __str__(self):
        return f"{self.pet.name} - {self.title} (Due: {self.due_date.date()})"

    @property
    def is_overdue(self):
        """Check if reminder is overdue"""
        from django.utils import timezone
        return not self.is_completed and self.due_date < timezone.now()

    @property
    def is_due_soon(self):
        """Check if reminder is due within 24 hours"""
        from django.utils import timezone
        from datetime import timedelta
        return not self.is_completed and self.due_date <= timezone.now() + timedelta(hours=24)


class PetDocument(models.Model):
    """Model for storing important pet documents"""
    DOCUMENT_TYPE_CHOICES = (
        ('adoption', 'Adoption Papers'),
        ('registration', 'Registration Certificate'),
        ('pedigree', 'Pedigree'),
        ('insurance', 'Insurance Policy'),
        ('medical', 'Medical Records'),
        ('vaccination', 'Vaccination Certificate'),
        ('microchip', 'Microchip Information'),
        ('license', 'Pet License'),
        ('other', 'Other'),
    )

    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to='pet_documents')
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    issuing_authority = models.CharField(max_length=200, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.pet.name} - {self.title}"

    @property
    def is_expiring_soon(self):
        """Check if document expires within 30 days"""
        if not self.expiry_date:
            return False
        from datetime import date, timedelta
        return self.expiry_date <= date.today() + timedelta(days=30)

    @property
    def is_expired(self):
        """Check if document is expired"""
        if not self.expiry_date:
            return False
        from datetime import date
        return self.expiry_date < date.today()
