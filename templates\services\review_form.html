{% extends 'base.html' %}
{% load static %}

{% block title %}Write Review - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="review-header">
        <a href="{% url 'booking-detail' booking.pk %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Booking
        </a>
        <h1>Write a Review</h1>
        <p>Share your experience with this service</p>
    </div>

    <div class="review-container">
        <div class="booking-summary">
            <div class="summary-card">
                <h3>Service Details</h3>
                <div class="service-info">
                    <h4>{{ booking.service.name }}</h4>
                    <p class="provider-name">
                        <i class="fas fa-user"></i>
                        {{ booking.service.provider.user.get_full_name|default:booking.service.provider.user.username }}
                    </p>
                    <div class="booking-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ booking.date|date:"F d, Y" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ booking.start_time|time:"g:i A" }} - {{ booking.end_time|time:"g:i A" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span>${{ booking.total_price }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="review-form-container">
            <div class="form-card">
                <h3>Your Review</h3>
                <form method="post" class="review-form">
                    {% csrf_token %}
                    
                    <div class="form-group">
                        {{ form.rating.label_tag }}
                        <div class="rating-input">
                            {{ form.rating }}
                            <div class="star-display" id="star-display">
                                {% for i in "12345" %}
                                    <i class="far fa-star" data-rating="{{ forloop.counter }}"></i>
                                {% endfor %}
                            </div>
                        </div>
                        {% if form.rating.errors %}
                            <div class="form-errors">
                                {{ form.rating.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        {{ form.comment.label_tag }}
                        {{ form.comment }}
                        {% if form.comment.errors %}
                            <div class="form-errors">
                                {{ form.comment.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-actions">
                        <a href="{% url 'booking-detail' booking.pk %}" class="btn btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-star"></i>
                            Submit Review
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.review-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.review-header h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.review-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
    margin: 0;
}

.review-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    max-width: 1000px;
    margin: 0 auto;
}

.summary-card, .form-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
}

.summary-card h3, .form-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-xl);
}

.service-info h4 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-lg);
}

.provider-name {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-sm);
}

.provider-name i {
    margin-right: var(--spacing-xs);
}

.booking-details {
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-700);
    padding: var(--spacing-xs) 0;
}

.detail-item i {
    width: 16px;
    color: var(--primary);
}

.review-form {
    display: grid;
    gap: var(--spacing-lg);
}

.form-group {
    display: grid;
    gap: var(--spacing-xs);
}

.form-group label {
    color: var(--gray-700);
    font-weight: 500;
    font-size: var(--font-sm);
}

.rating-input {
    position: relative;
}

.rating-input select {
    opacity: 0;
    position: absolute;
    pointer-events: none;
}

.star-display {
    display: flex;
    gap: var(--spacing-xs);
    font-size: var(--font-xl);
    cursor: pointer;
}

.star-display i {
    color: var(--gray-300);
    transition: var(--transition-base);
}

.star-display i:hover,
.star-display i.active {
    color: var(--warning);
}

.form-group textarea {
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-base);
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    transition: var(--transition-base);
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-errors {
    color: var(--danger);
    font-size: var(--font-sm);
}

.form-actions {
    display: flex;
    gap: var(--spacing-r);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
    .review-container {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.star-display i');
    const ratingSelect = document.querySelector('#id_rating');
    
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingSelect.value = rating;
            updateStarDisplay(rating);
        });
        
        star.addEventListener('mouseenter', function() {
            const rating = index + 1;
            highlightStars(rating);
        });
    });
    
    document.querySelector('.star-display').addEventListener('mouseleave', function() {
        const currentRating = ratingSelect.value || 0;
        updateStarDisplay(currentRating);
    });
    
    function updateStarDisplay(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star active';
            } else {
                star.className = 'far fa-star';
            }
        });
    }
    
    function highlightStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star active';
            } else {
                star.className = 'far fa-star';
            }
        });
    }
    
    // Initialize star display
    const initialRating = ratingSelect.value || 0;
    updateStarDisplay(initialRating);
});
</script>
{% endblock %}
