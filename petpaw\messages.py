"""
Centralized messages for the PetPaw application.
This file contains all success and error messages used throughout the application
for easy editing and consistency.
"""

# Authentication Messages
AUTH_MESSAGES = {
    'login_success': 'Welcome back to PetPaw!',
    'login_failed': 'Invalid email/phone number or password.',
    'logout_success': 'You have been logged out successfully.',
    'registration_success': 'Welcome to PetPaw! Your account has been created successfully.',
    'registration_failed': 'There was an error creating your account. Please try again.',
    'profile_completion_required': 'Please complete your profile to continue using PetPaw.',
    'profile_completed': 'Your profile has been completed successfully!',
    'email_already_exists': 'A user with this email already exists.',
    'phone_already_exists': 'This phone number is already registered.',
    'invalid_credentials': 'Please enter a valid email or phone number and password.',
}

# Profile Messages
PROFILE_MESSAGES = {
    'profile_updated': 'Your profile has been updated successfully!',
    'profile_update_failed': 'There was an error updating your profile.',
    'preferences_updated': 'Your preferences have been updated successfully!',
    'follow_success': 'You are now following {name}.',
    'unfollow_success': 'You have unfollowed {name}.',
    'cannot_follow_self': 'You cannot follow yourself.',
    'address_added': 'Address has been added successfully!',
    'address_updated': 'Address has been updated successfully!',
    'address_deleted': 'Address has been deleted successfully!',
}

# Pet Messages
PET_MESSAGES = {
    'pet_added': 'Your pet has been added successfully!',
    'pet_updated': 'Pet information has been updated successfully!',
    'pet_deleted': 'Pet has been removed successfully.',
    'pet_adoption_status_changed': 'Pet adoption status has been updated.',
    'health_record_added': 'Health record has been added successfully!',
    'vaccination_added': 'Vaccination record has been added successfully!',
    'weight_recorded': 'Weight has been recorded successfully!',
    'milestone_added': 'Milestone has been added successfully!',
    'reminder_added': 'Reminder has been set successfully!',
    'reminder_completed': 'Reminder has been marked as completed.',
}

# Shop Messages
SHOP_MESSAGES = {
    'product_added_to_cart': 'Product has been added to your cart!',
    'product_removed_from_cart': 'Product has been removed from your cart.',
    'cart_updated': 'Your cart has been updated.',
    'order_placed': 'Your order has been placed successfully!',
    'order_cancelled': 'Your order has been cancelled.',
    'payment_successful': 'Payment processed successfully!',
    'payment_failed': 'Payment failed. Please try again.',
    'wishlist_added': 'Item has been added to your wishlist!',
    'wishlist_removed': 'Item has been removed from your wishlist.',
}

# Service Messages
SERVICE_MESSAGES = {
    'service_provider_registered': 'You have been registered as a service provider!',
    'service_added': 'Your service has been added successfully!',
    'service_updated': 'Service information has been updated successfully!',
    'service_deleted': 'Service has been removed successfully.',
    'booking_requested': 'Your booking request has been sent!',
    'booking_confirmed': 'Your booking has been confirmed.',
    'booking_cancelled': 'Booking has been cancelled.',
    'review_submitted': 'Your review has been submitted successfully!',
}

# Messaging Messages
MESSAGING_MESSAGES = {
    'message_sent': 'Message sent successfully!',
    'message_failed': 'Failed to send message. Please try again.',
    'inquiry_sent': 'Your inquiry has been sent successfully!',
    'conversation_started': 'Conversation started successfully!',
    'notification_marked_read': 'Notification marked as read.',
    'all_notifications_read': 'All notifications marked as read.',
}

# General Messages
GENERAL_MESSAGES = {
    'success': 'Operation completed successfully!',
    'error': 'An error occurred. Please try again.',
    'permission_denied': 'You do not have permission to perform this action.',
    'not_found': 'The requested item was not found.',
    'invalid_request': 'Invalid request. Please check your input.',
    'server_error': 'A server error occurred. Please try again later.',
    'maintenance_mode': 'The site is currently under maintenance. Please try again later.',
}

# Validation Messages
VALIDATION_MESSAGES = {
    'required_field': 'This field is required.',
    'invalid_email': 'Please enter a valid email address.',
    'invalid_phone': 'Please enter a valid phone number.',
    'password_mismatch': 'Passwords do not match.',
    'weak_password': 'Password is too weak. Please choose a stronger password.',
    'file_too_large': 'File size is too large. Maximum size allowed is {max_size}.',
    'invalid_file_type': 'Invalid file type. Allowed types: {allowed_types}.',
}

# Helper function to get messages
def get_message(category, key, **kwargs):
    """
    Get a message from the specified category and format it with provided kwargs.
    
    Args:
        category (str): Message category (e.g., 'auth', 'profile', 'pet')
        key (str): Message key within the category
        **kwargs: Format parameters for the message
    
    Returns:
        str: Formatted message or a default error message if not found
    """
    message_categories = {
        'auth': AUTH_MESSAGES,
        'profile': PROFILE_MESSAGES,
        'pet': PET_MESSAGES,
        'shop': SHOP_MESSAGES,
        'service': SERVICE_MESSAGES,
        'messaging': MESSAGING_MESSAGES,
        'general': GENERAL_MESSAGES,
        'validation': VALIDATION_MESSAGES,
    }
    
    category_messages = message_categories.get(category, {})
    message = category_messages.get(key, GENERAL_MESSAGES['error'])
    
    try:
        return message.format(**kwargs)
    except KeyError:
        return message
