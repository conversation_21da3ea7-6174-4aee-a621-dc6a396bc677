from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import User, UserProfile, UserPreferences, UserRole


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile and UserPreferences when a new User is created"""
    if created:
        # Use get_or_create to avoid duplicate profile creation
        UserProfile.objects.get_or_create(user=instance)
        UserPreferences.objects.get_or_create(user=instance)

        # Assign default role
        try:
            regular_role = UserRole.objects.get(name='regular')
            instance.roles.add(regular_role)
            if not instance.primary_role:
                instance.primary_role = regular_role
                instance.save()
        except UserRole.DoesNotExist:
            pass  # Role will be created by data migration


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the UserProfile when the User is saved"""
    # Only save if profile exists to avoid errors
    if hasattr(instance, 'profile'):
        try:
            instance.profile.save()
        except UserProfile.DoesNotExist:
            # If profile doesn't exist, create it
            UserProfile.objects.create(user=instance)
