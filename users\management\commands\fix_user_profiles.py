from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User, UserProfile


class Command(BaseCommand):
    help = 'Fix missing UserProfile instances and check for database inconsistencies'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Find users without profiles
        users_without_profiles = User.objects.filter(profile__isnull=True)
        
        if users_without_profiles.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'Found {users_without_profiles.count()} users without profiles:'
                )
            )
            
            for user in users_without_profiles:
                self.stdout.write(f'  - {user.username} (ID: {user.id})')
                
                if not dry_run:
                    with transaction.atomic():
                        profile, created = UserProfile.objects.get_or_create(user=user)
                        if created:
                            self.stdout.write(
                                self.style.SUCCESS(f'    Created profile for {user.username}')
                            )
                        else:
                            self.stdout.write(
                                self.style.WARNING(f'    Profile already exists for {user.username}')
                            )
        else:
            self.stdout.write(self.style.SUCCESS('All users have profiles'))
        
        # Check for orphaned profiles
        orphaned_profiles = UserProfile.objects.filter(user__isnull=True)
        
        if orphaned_profiles.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'Found {orphaned_profiles.count()} orphaned profiles (profiles without users):'
                )
            )
            
            for profile in orphaned_profiles:
                self.stdout.write(f'  - Profile ID: {profile.id}')
                
                if not dry_run:
                    profile.delete()
                    self.stdout.write(
                        self.style.SUCCESS(f'    Deleted orphaned profile {profile.id}')
                    )
        else:
            self.stdout.write(self.style.SUCCESS('No orphaned profiles found'))
        
        # Check for duplicate profiles
        from django.db.models import Count
        users_with_multiple_profiles = User.objects.annotate(
            profile_count=Count('profile')
        ).filter(profile_count__gt=1)
        
        if users_with_multiple_profiles.exists():
            self.stdout.write(
                self.style.ERROR(
                    f'Found {users_with_multiple_profiles.count()} users with multiple profiles:'
                )
            )
            
            for user in users_with_multiple_profiles:
                profiles = UserProfile.objects.filter(user=user)
                self.stdout.write(f'  - {user.username} has {profiles.count()} profiles')
                
                if not dry_run:
                    # Keep the first profile, delete the rest
                    profiles_to_delete = profiles[1:]
                    for profile in profiles_to_delete:
                        profile.delete()
                        self.stdout.write(
                            self.style.SUCCESS(f'    Deleted duplicate profile {profile.id}')
                        )
        else:
            self.stdout.write(self.style.SUCCESS('No users with multiple profiles found'))
        
        if not dry_run:
            self.stdout.write(self.style.SUCCESS('Database cleanup completed'))
        else:
            self.stdout.write(self.style.WARNING('Run without --dry-run to apply changes'))
