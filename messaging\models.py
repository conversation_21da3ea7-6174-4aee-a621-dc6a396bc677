from django.db import models
from users.models import User


class Conversation(models.Model):
    """Model for conversations between users"""
    participants = models.ManyToManyField(User, related_name='conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"Conversation {self.id} between {', '.join(user.username for user in self.participants.all())}"

    def get_other_participant(self, user):
        """Get the other participant in a two-person conversation"""
        return self.participants.exclude(id=user.id).first()

    def get_last_message(self):
        """Get the last message in the conversation"""
        return self.messages.order_by('-created_at').first()


class Message(models.Model):
    """Model for messages within a conversation"""
    MESSAGE_TYPES = (
        ('regular', 'Regular Message'),
        ('pet_inquiry', 'Pet Inquiry'),
        ('pet_response', 'Pet Inquiry Response'),
    )

    INQUIRY_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('interested', 'Owner Interested'),
        ('not_available', 'Not Available'),
        ('sold', 'Sold'),
        ('declined', 'Declined'),
    )

    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()
    image = models.ImageField(upload_to='message_images', blank=True, null=True)

    # Message type and related objects
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='regular')
    pet = models.ForeignKey('pets.Pet', on_delete=models.CASCADE, null=True, blank=True, related_name='inquiry_messages')

    # Pet inquiry specific fields
    inquiry_status = models.CharField(max_length=20, choices=INQUIRY_STATUS_CHOICES, null=True, blank=True)

    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"

    def mark_as_read(self):
        """Mark message as read"""
        if not self.is_read:
            self.is_read = True
            self.save(update_fields=['is_read'])


class Notification(models.Model):
    """Simple notification model for messaging"""
    NOTIFICATION_TYPES = (
        ('message', 'New Message'),
        ('pet_inquiry', 'Pet Inquiry'),
        ('pet_inquiry_response', 'Pet Inquiry Response'),
    )

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, null=True, blank=True)
    pet = models.ForeignKey('pets.Pet', on_delete=models.CASCADE, null=True, blank=True, related_name='notifications')
    message = models.CharField(max_length=255)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Notification for {self.recipient.username} from {self.sender.username}"
