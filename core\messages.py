"""
Centralized message constants for the PetPaw application.
This file contains all success, error, warning, and info messages used throughout the app.
"""

# Pet Management Messages
PET_MESSAGES = {
    'success': {
        'created': 'Pet profile created successfully! Welcome to the PetPaw family!',
        'updated': 'Pet profile updated successfully!',
        'deleted': 'Pet profile has been removed.',
        'followed': 'You are now following this adorable pet!',
        'unfollowed': 'You have unfollowed this pet.',
        'photo_added': 'Photo added to pet gallery successfully!',
        'photo_deleted': 'Photo removed from gallery.',
    },
    'error': {
        'not_owner': 'You can only manage your own pets.',
        'not_found': 'Pet not found.',
        'photo_upload_failed': 'Failed to upload photo. Please try again.',
        'invalid_data': 'Please check your input and try again.',
        'permission_denied': 'You do not have permission to perform this action.',
    },
    'warning': {
        'no_photos': 'This pet doesn\'t have any photos yet.',
        'profile_incomplete': 'Consider adding more details to your pet\'s profile.',
    },
    'info': {
        'adoption_available': 'This pet is available for adoption.',
        'private_profile': 'This is a private pet profile.',
    }
}

# Health Tracking Messages
HEALTH_MESSAGES = {
    'success': {
        'vaccination_added': 'Vaccination record added successfully!',
        'vaccination_updated': 'Vaccination record updated successfully!',
        'vaccination_deleted': 'Vaccination record removed.',
        'medical_record_added': 'Medical record added successfully!',
        'medical_record_updated': 'Medical record updated successfully!',
        'medical_record_deleted': 'Medical record removed.',
        'growth_recorded': 'Growth record added successfully!',
        'weight_logged': 'Weight logged successfully!',
        'milestone_added': 'Milestone added successfully!',
        'milestone_updated': 'Milestone updated successfully!',
        'milestone_deleted': 'Milestone removed.',
        'reminder_added': 'Reminder created successfully!',
        'reminder_updated': 'Reminder updated successfully!',
        'reminder_completed': 'Reminder marked as complete!',
        'reminder_deleted': 'Reminder removed.',
        'exercise_logged': 'Exercise activity logged successfully!',
        'feeding_logged': 'Feeding record added successfully!',
    },
    'error': {
        'not_owner': 'You can only manage health records for your own pets.',
        'vaccination_failed': 'Failed to add vaccination record. Please try again.',
        'medical_record_failed': 'Failed to add medical record. Please try again.',
        'growth_record_failed': 'Failed to record growth data. Please try again.',
        'weight_log_failed': 'Failed to log weight. Please check your input.',
        'milestone_failed': 'Failed to add milestone. Please try again.',
        'reminder_failed': 'Failed to create reminder. Please try again.',
        'reminder_complete_failed': 'Failed to complete reminder. Please try again.',
        'exercise_log_failed': 'Failed to log exercise. Please try again.',
        'feeding_log_failed': 'Failed to log feeding. Please try again.',
        'invalid_weight': 'Please enter a valid weight value.',
        'invalid_date': 'Please enter a valid date.',
        'future_date': 'Date cannot be in the future.',
    },
    'warning': {
        'vaccination_overdue': 'Some vaccinations are overdue!',
        'checkup_due': 'Annual checkup is due soon.',
        'weight_not_recorded': 'No weight records found for this pet.',
        'no_reminders': 'No upcoming reminders.',
    },
    'info': {
        'vaccination_due_soon': 'Vaccination due in the next 30 days.',
        'growth_tracking_tip': 'Regular weight tracking helps monitor your pet\'s health.',
        'milestone_suggestion': 'Consider adding milestones to track your pet\'s development.',
    }
}

# User Account Messages
USER_MESSAGES = {
    'success': {
        'profile_updated': 'Your profile has been updated successfully!',
        'password_changed': 'Password changed successfully!',
        'email_updated': 'Email address updated successfully!',
        'account_created': 'Welcome to PetPaw! Your account has been created successfully.',
        'login_success': 'Welcome back to PetPaw!',
        'logout_success': 'You have been logged out successfully.',
    },
    'error': {
        'login_failed': 'Invalid email or password. Please try again.',
        'registration_failed': 'Registration failed. Please check your information.',
        'password_mismatch': 'Passwords do not match.',
        'email_exists': 'An account with this email already exists.',
        'weak_password': 'Password is too weak. Please choose a stronger password.',
        'profile_update_failed': 'Failed to update profile. Please try again.',
    },
    'warning': {
        'profile_incomplete': 'Please complete your profile to get the most out of PetPaw.',
        'email_not_verified': 'Please verify your email address.',
    },
    'info': {
        'password_requirements': 'Password must be at least 8 characters long.',
        'profile_visibility': 'Your profile is visible to other PetPaw users.',
    }
}

# Service Provider Messages
SERVICE_MESSAGES = {
    'success': {
        'provider_registered': 'Service provider profile created successfully!',
        'provider_updated': 'Service provider profile updated successfully!',
        'service_added': 'Service added successfully!',
        'service_updated': 'Service updated successfully!',
        'service_deleted': 'Service removed successfully!',
        'booking_confirmed': 'Booking confirmed successfully!',
        'booking_cancelled': 'Booking cancelled successfully!',
    },
    'error': {
        'provider_registration_failed': 'Failed to register as service provider. Please try again.',
        'service_add_failed': 'Failed to add service. Please check your information.',
        'booking_failed': 'Booking failed. Please try again.',
        'not_service_provider': 'You must be a registered service provider to perform this action.',
        'service_not_available': 'This service is currently not available.',
    },
    'warning': {
        'pending_approval': 'Your service provider application is pending approval.',
        'incomplete_profile': 'Please complete your service provider profile.',
    },
    'info': {
        'provider_benefits': 'As a service provider, you can offer services to pet owners in your area.',
        'booking_guidelines': 'Please review our booking guidelines before confirming.',
    }
}

# Marketplace Messages
MARKETPLACE_MESSAGES = {
    'success': {
        'inquiry_sent': 'Your inquiry has been sent to the pet owner!',
        'inquiry_responded': 'Response sent successfully!',
        'pet_marked_available': 'Pet marked as available for adoption!',
        'pet_marked_unavailable': 'Pet marked as no longer available.',
        'adoption_confirmed': 'Adoption process initiated successfully!',
    },
    'error': {
        'inquiry_failed': 'Failed to send inquiry. Please try again.',
        'response_failed': 'Failed to send response. Please try again.',
        'not_available': 'This pet is no longer available for adoption.',
        'own_pet_inquiry': 'You cannot inquire about your own pet.',
        'already_inquired': 'You have already sent an inquiry for this pet.',
    },
    'warning': {
        'adoption_responsibility': 'Pet adoption is a serious commitment. Please consider carefully.',
        'inquiry_pending': 'Your inquiry is pending response from the owner.',
    },
    'info': {
        'adoption_process': 'The adoption process may take several days to complete.',
        'meet_before_adopt': 'We recommend meeting the pet before finalizing adoption.',
    }
}

# Messaging System Messages
MESSAGING_MESSAGES = {
    'success': {
        'message_sent': 'Message sent successfully!',
        'conversation_started': 'Conversation started successfully!',
        'message_deleted': 'Message deleted successfully!',
    },
    'error': {
        'message_send_failed': 'Failed to send message. Please try again.',
        'message_too_long': 'Message is too long. Please keep it under 1000 characters.',
        'recipient_not_found': 'Recipient not found.',
        'conversation_not_found': 'Conversation not found.',
    },
    'warning': {
        'message_limit_reached': 'You have reached your daily message limit.',
        'blocked_user': 'You cannot send messages to this user.',
    },
    'info': {
        'message_guidelines': 'Please keep messages respectful and relevant to pet care.',
        'response_expected': 'Most users respond within 24 hours.',
    }
}

# General System Messages
SYSTEM_MESSAGES = {
    'success': {
        'operation_completed': 'Operation completed successfully!',
        'data_saved': 'Data saved successfully!',
        'settings_updated': 'Settings updated successfully!',
    },
    'error': {
        'unexpected_error': 'An unexpected error occurred. Please try again.',
        'server_error': 'Server error. Please try again later.',
        'network_error': 'Network error. Please check your connection.',
        'file_too_large': 'File is too large. Maximum size is 10MB.',
        'invalid_file_type': 'Invalid file type. Please upload a valid image.',
        'permission_denied': 'You do not have permission to perform this action.',
    },
    'warning': {
        'unsaved_changes': 'You have unsaved changes. Are you sure you want to leave?',
        'data_loss_warning': 'This action cannot be undone.',
    },
    'info': {
        'loading': 'Loading...',
        'processing': 'Processing your request...',
        'maintenance_mode': 'The system is currently under maintenance.',
    }
}

# Utility function to get messages
def get_message(category, message_type, key):
    """
    Get a specific message from the message constants.

    Args:
        category (str): Message category (e.g., 'PET', 'HEALTH', 'USER')
        message_type (str): Message type ('success', 'error', 'warning', 'info')
        key (str): Specific message key

    Returns:
        str: The message text or a default message if not found
    """
    message_dict = {
        'PET': PET_MESSAGES,
        'HEALTH': HEALTH_MESSAGES,
        'USER': USER_MESSAGES,
        'SERVICE': SERVICE_MESSAGES,
        'MARKETPLACE': MARKETPLACE_MESSAGES,
        'MESSAGING': MESSAGING_MESSAGES,
        'SYSTEM': SYSTEM_MESSAGES,
    }

    try:
        return message_dict[category][message_type][key]
    except KeyError:
        return f"Message not found: {category}.{message_type}.{key}"

# Quick access functions for common message types
def success_message(category, key):
    """Get a success message"""
    return get_message(category, 'success', key)

def error_message(category, key):
    """Get an error message"""
    return get_message(category, 'error', key)

def warning_message(category, key):
    """Get a warning message"""
    return get_message(category, 'warning', key)

def info_message(category, key):
    """Get an info message"""
    return get_message(category, 'info', key)
