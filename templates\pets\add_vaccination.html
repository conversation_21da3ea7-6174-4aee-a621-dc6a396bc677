{% extends 'base.html' %}

{% block title %}Add Vaccination for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .vaccination-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .vaccination-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .vaccination-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xl);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .file-upload-container:hover {
        border-color: var(--primary);
        background-color: var(--gray-50);
    }
    
    .file-upload-icon {
        font-size: 2rem;
        color: var(--gray-400);
        margin-bottom: var(--spacing-md);
    }
    
    .file-upload-text {
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }
    
    .file-upload-info {
        font-size: var(--font-sm);
        color: var(--gray-500);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="vaccination-form-container">
        <div class="vaccination-form-header">
            <h1>Add Vaccination Record for {{ pet.name }}</h1>
            <p>Keep track of your pet's vaccination history</p>
        </div>
        
        <div class="vaccination-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Vaccination Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.vaccine_type.id_for_label }}" class="form-label">Vaccine Type</label>
                            {{ form.vaccine_type }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.vaccine_name.id_for_label }}" class="form-label">Vaccine Name/Brand</label>
                            {{ form.vaccine_name }}
                            <small class="form-text">Specific name or brand of the vaccine</small>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.date_administered.id_for_label }}" class="form-label">Date Administered</label>
                            {{ form.date_administered }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.next_due_date.id_for_label }}" class="form-label">Next Due Date</label>
                            {{ form.next_due_date }}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.batch_number.id_for_label }}" class="form-label">Batch Number (Optional)</label>
                        {{ form.batch_number }}
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Veterinarian Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.veterinarian.id_for_label }}" class="form-label">Veterinarian</label>
                            {{ form.veterinarian }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.clinic.id_for_label }}" class="form-label">Clinic</label>
                            {{ form.clinic }}
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Additional Information</h2>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                        {{ form.notes }}
                    </div>
                    
                    <div class="file-upload-container" id="certificate-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-file-alt"></i>
                        </div>
                        <div class="file-upload-text">Click to upload vaccination certificate (optional)</div>
                        <div class="file-upload-info">PDF, DOC, or JPG • Max 10MB</div>
                        <div style="display:none;">{{ form.certificate }}</div>
                    </div>
                    
                    <div id="file-name-display" class="form-text"></div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Vaccination</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // File upload handling
        const certificateUploadContainer = document.getElementById('certificate-upload-container');
        const certificateInput = document.querySelector('input[name="certificate"]');
        const fileNameDisplay = document.getElementById('file-name-display');
        
        if (certificateUploadContainer && certificateInput) {
            certificateUploadContainer.addEventListener('click', function() {
                certificateInput.click();
            });
            
            certificateInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileName = this.files[0].name;
                    fileNameDisplay.textContent = `Selected file: ${fileName}`;
                    certificateUploadContainer.style.borderColor = 'var(--primary)';
                } else {
                    fileNameDisplay.textContent = '';
                    certificateUploadContainer.style.borderColor = 'var(--gray-300)';
                }
            });
        }
    });
</script>
{% endblock %}
