from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.core.exceptions import ValidationError
from .models import User, UserProfile, Address
from core.messages import error_message


class CustomUserCreationForm(UserCreationForm):
    """Form for user registration"""
    full_name = forms.CharField(
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your full name'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address'
        })
    )
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your phone number'
        })
    )

    class Meta:
        model = User
        fields = ('full_name', 'email', 'phone_number', 'password1', 'password2')

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError(error_message('USER', 'email_exists'))
        return email

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if User.objects.filter(phone_number=phone_number).exists():
            raise ValidationError(error_message('USER', 'phone_exists'))
        return phone_number

    def save(self, commit=True):
        user = super().save(commit=False)
        user.full_name = self.cleaned_data['full_name']
        user.email = self.cleaned_data['email']
        user.phone_number = self.cleaned_data['phone_number']
        user.profile_completed = True
        # Generate a unique username based on email
        user.username = self.cleaned_data['email']
        if commit:
            user.save()
        return user


class UserProfileForm(forms.ModelForm):
    """Form for updating user profile"""
    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'email', 'bio', 'location',
                  'birth_date', 'profile_picture', 'phone_number')
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
        }


class ExtendedProfileForm(forms.ModelForm):
    """Form for updating extended profile information"""
    class Meta:
        model = UserProfile
        fields = ('website', 'interests')


class CustomLoginForm(forms.Form):
    """Custom login form that accepts email or phone number"""
    login = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Email or Phone Number'
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password'
        })
    )
    remember = forms.BooleanField(required=False)


class ProfileCompletionForm(forms.ModelForm):
    """Form for existing users to complete their profile"""
    full_name = forms.CharField(
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your full name'
        })
    )
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your phone number'
        })
    )

    class Meta:
        model = User
        fields = ('full_name', 'phone_number')

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if User.objects.filter(phone_number=phone_number).exclude(pk=self.instance.pk).exists():
            raise ValidationError(error_message('USER', 'phone_exists'))
        return phone_number

    def save(self, commit=True):
        user = super().save(commit=False)
        user.profile_completed = True
        if commit:
            user.save()
        return user


class AddressForm(forms.ModelForm):
    """Form for adding/updating user addresses"""
    class Meta:
        model = Address
        fields = ('address_type', 'street_address', 'apartment_address',
                  'city', 'state', 'country', 'zip_code', 'default')
        widgets = {
            'address_type': forms.Select(attrs={'class': 'form-select'}),
        }


class LoginForm(forms.Form):
    login = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'id': 'default-input',
            'placeholder': 'Enter your username or email'
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your password'
        })
    )
    remember = forms.BooleanField(required=False)
