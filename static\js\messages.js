/**
 * PetPaw Message System
 * Consistent message handling for success, error, warning, and info messages
 */

class MessageSystem {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        // Create message container if it doesn't exist
        this.container = document.getElementById('message-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'message-container';
            this.container.className = 'message-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Show a message
     * @param {string} text - Message text
     * @param {string} type - Message type (success, error, warning, info)
     * @param {number} duration - Auto-hide duration in milliseconds (0 = no auto-hide)
     */
    show(text, type = 'info', duration = 5000) {
        const message = this.createMessage(text, type);
        this.container.appendChild(message);

        // Auto-hide after duration
        if (duration > 0) {
            setTimeout(() => {
                this.hide(message);
            }, duration);
        }

        return message;
    }

    /**
     * Create a message element
     * @param {string} text - Message text
     * @param {string} type - Message type
     */
    createMessage(text, type) {
        const message = document.createElement('div');
        message.className = `message message-${type}`;
        
        const icon = this.getIcon(type);
        
        message.innerHTML = `
            <div class="message-icon">
                <i class="${icon}"></i>
            </div>
            <div class="message-text">${text}</div>
            <button type="button" class="message-close" onclick="messageSystem.hide(this.parentElement)">
                <i class="fas fa-times"></i>
            </button>
        `;

        return message;
    }

    /**
     * Get icon class for message type
     * @param {string} type - Message type
     */
    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * Hide a message
     * @param {HTMLElement} message - Message element to hide
     */
    hide(message) {
        if (message && message.parentNode) {
            message.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                if (message.parentNode) {
                    message.remove();
                }
            }, 300);
        }
    }

    /**
     * Show success message
     * @param {string} text - Message text
     * @param {number} duration - Auto-hide duration
     */
    success(text, duration = 5000) {
        return this.show(text, 'success', duration);
    }

    /**
     * Show error message
     * @param {string} text - Message text
     * @param {number} duration - Auto-hide duration (0 = no auto-hide for errors)
     */
    error(text, duration = 0) {
        return this.show(text, 'error', duration);
    }

    /**
     * Show warning message
     * @param {string} text - Message text
     * @param {number} duration - Auto-hide duration
     */
    warning(text, duration = 7000) {
        return this.show(text, 'warning', duration);
    }

    /**
     * Show info message
     * @param {string} text - Message text
     * @param {number} duration - Auto-hide duration
     */
    info(text, duration = 5000) {
        return this.show(text, 'info', duration);
    }

    /**
     * Clear all messages
     */
    clear() {
        const messages = this.container.querySelectorAll('.message');
        messages.forEach(message => this.hide(message));
    }
}

// Initialize global message system
const messageSystem = new MessageSystem();

// Convenience functions for global access
function showSuccessMessage(text, duration = 5000) {
    return messageSystem.success(text, duration);
}

function showErrorMessage(text, duration = 0) {
    return messageSystem.error(text, duration);
}

function showWarningMessage(text, duration = 7000) {
    return messageSystem.warning(text, duration);
}

function showInfoMessage(text, duration = 5000) {
    return messageSystem.info(text, duration);
}

function clearAllMessages() {
    messageSystem.clear();
}

// AJAX helper functions
function handleAjaxSuccess(response, defaultMessage = 'Operation completed successfully!') {
    if (response.success) {
        const message = response.message || defaultMessage;
        showSuccessMessage(message);
        return true;
    } else {
        const error = response.error || 'An error occurred. Please try again.';
        showErrorMessage(error);
        return false;
    }
}

function handleAjaxError(xhr, status, error) {
    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    if (xhr.status === 403) {
        errorMessage = 'You do not have permission to perform this action.';
    } else if (xhr.status === 404) {
        errorMessage = 'The requested resource was not found.';
    } else if (xhr.status === 500) {
        errorMessage = 'Server error. Please try again later.';
    } else if (status === 'timeout') {
        errorMessage = 'Request timed out. Please check your connection and try again.';
    } else if (status === 'error' && xhr.status === 0) {
        errorMessage = 'Network error. Please check your connection.';
    }
    
    showErrorMessage(errorMessage);
}

// CSRF token helper
function getCsrfToken() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return decodeURIComponent(value);
        }
    }
    return null;
}

// Enhanced AJAX wrapper with consistent error handling
function makeAjaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'same-origin'
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    // Merge headers
    if (options.headers) {
        finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }

    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('AJAX Error:', error);
            handleAjaxError({ status: 0 }, 'error', error.message);
            throw error;
        });
}

// Export for module systems (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MessageSystem,
        messageSystem,
        showSuccessMessage,
        showErrorMessage,
        showWarningMessage,
        showInfoMessage,
        clearAllMessages,
        handleAjaxSuccess,
        handleAjaxError,
        makeAjaxRequest
    };
}
