from django.urls import path
from . import views

urlpatterns = [
    # Public service pages
    path('', views.ServicesHomeView.as_view(), name='services-home'),
    path('categories/', views.ServiceCategoryListView.as_view(), name='service-categories'),
    path('categories/<slug:slug>/providers/', views.CategoryProvidersView.as_view(), name='category-providers'),
    path('providers/', views.ServiceProviderListView.as_view(), name='provider-list'),
    path('providers/<int:pk>/', views.ServiceProviderDetailView.as_view(), name='provider-detail'),
    path('become-provider/', views.become_provider, name='become-provider'),
    path('book-provider/<int:pk>/', views.book_provider, name='book-provider'),
    path('book-service/<int:pk>/', views.book_service, name='book-service'),
    path('bookings/', views.booking_list, name='booking-list'),
    path('bookings/<int:pk>/', views.booking_detail, name='booking-detail'),
    path('bookings/<int:pk>/update-status/', views.update_booking_status, name='update-booking-status'),
    path('bookings/<int:pk>/review/', views.add_service_review, name='add-service-review'),

    # Provider dashboard
    path('dashboard/', views.provider_dashboard, name='provider-dashboard'),
    path('dashboard/services/', views.provider_services, name='provider-services'),
    path('dashboard/bookings/', views.provider_bookings, name='provider-bookings'),
    path('dashboard/availability/', views.provider_availability, name='provider-availability'),
    path('dashboard/settings/', views.provider_settings, name='provider-settings'),
    path('dashboard/toggle-availability/', views.provider_toggle_availability, name='provider-toggle-availability'),

    # Service management
    path('add-service/', views.ServiceCreateView.as_view(), name='add-service'),
    path('update-service/<int:pk>/', views.ServiceUpdateView.as_view(), name='update-service'),
    path('delete-service/<int:pk>/', views.ServiceDeleteView.as_view(), name='delete-service'),
    path('add-availability/', views.add_availability, name='add-availability'),
    path('delete-availability/<int:pk>/', views.delete_availability, name='delete-availability'),

    # Gallery management
    path('add-gallery-image/', views.add_gallery_image, name='add-gallery-image'),
    path('delete-gallery-image/<int:pk>/', views.delete_gallery_image, name='delete-gallery-image'),
]
