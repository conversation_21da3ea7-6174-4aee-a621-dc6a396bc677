{% extends 'base.html' %}
{% load static %}

{% block title %}My Wishlist - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="profile-header">
                <h1 class="page-title">My Wishlist</h1>
                <p class="text-muted">Items you've saved for later</p>
            </div>

            <!-- Filter Tabs -->
            <div class="wishlist-filters">
                <ul class="nav nav-tabs">
                    <li class="nav-item">
                        <a class="nav-link {% if current_type == 'all' %}active{% endif %}"
                           href="?type=all">All Items</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if current_type == 'pet' %}active{% endif %}"
                           href="?type=pet">Pets</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if current_type == 'product' %}active{% endif %}"
                           href="?type=product">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if current_type == 'service' %}active{% endif %}"
                           href="?type=service">Services</a>
                    </li>
                </ul>
            </div>

            {% if wishlist_items %}
                <div class="wishlist-grid">
                    {% for item in wishlist_items %}
                        <div class="wishlist-card" data-item-id="{{ item.object_id }}" data-item-type="{{ item.item_type }}">
                            <div class="wishlist-item-header">
                                <span class="item-type-badge">{{ item.get_item_type_display }}</span>
                                <button class="remove-from-wishlist" onclick="removeFromWishlist({{ item.object_id }}, '{{ item.item_type }}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="wishlist-item-content">
                                {% if item.item_type == 'pet' %}
                                    <div class="item-image">
                                        <img src="{{ item.content_object.profile_picture.url }}"
                                             alt="{{ item.content_object.name }}"
                                             class="wishlist-thumbnail">
                                    </div>
                                    <div class="item-details">
                                        <h3 class="item-title">
                                            <a href="{{ item.content_object.get_absolute_url }}">
                                                {{ item.content_object.name }}
                                            </a>
                                        </h3>
                                        <p class="item-meta">
                                            {{ item.content_object.category.name }} •
                                            {% if item.content_object.breed %}{{ item.content_object.breed.name }}{% endif %}
                                        </p>
                                        <p class="item-owner">Owner: {{ item.content_object.owner.username }}</p>
                                        {% if item.content_object.is_for_adoption %}
                                            <span class="adoption-badge">Available for Adoption</span>
                                            {% if item.content_object.adoption_price %}
                                                <p class="adoption-price">${{ item.content_object.adoption_price }}</p>
                                            {% endif %}
                                        {% endif %}
                                    </div>

                                {% elif item.item_type == 'product' %}
                                    <div class="item-image">
                                        <img src="{{ item.content_object.image.url }}"
                                             alt="{{ item.content_object.name }}"
                                             class="wishlist-thumbnail">
                                    </div>
                                    <div class="item-details">
                                        <h3 class="item-title">
                                            <a href="{{ item.content_object.get_absolute_url }}">
                                                {{ item.content_object.name }}
                                            </a>
                                        </h3>
                                        <p class="item-meta">{{ item.content_object.category.name }}</p>
                                        <p class="item-price">
                                            {% if item.content_object.discount_price %}
                                                <span class="original-price">${{ item.content_object.price }}</span>
                                                <span class="discount-price">${{ item.content_object.discount_price }}</span>
                                            {% else %}
                                                <span class="current-price">${{ item.content_object.price }}</span>
                                            {% endif %}
                                        </p>
                                        {% if not item.content_object.is_available %}
                                            <span class="out-of-stock">Out of Stock</span>
                                        {% endif %}
                                    </div>

                                {% elif item.item_type == 'service' %}
                                    <div class="item-details service-item">
                                        <h3 class="item-title">
                                            <a href="{{ item.content_object.get_absolute_url }}">
                                                {{ item.content_object.name }}
                                            </a>
                                        </h3>
                                        <p class="item-meta">{{ item.content_object.category.name }}</p>
                                        <p class="service-provider">
                                            Provider: {{ item.content_object.provider.user.username }}
                                        </p>
                                        <p class="service-price">${{ item.content_object.price }}</p>
                                        <p class="service-duration">{{ item.content_object.duration }} minutes</p>
                                    </div>
                                {% endif %}

                                {% if item.notes %}
                                    <div class="item-notes">
                                        <small class="text-muted">
                                            <i class="fas fa-sticky-note"></i> {{ item.notes }}
                                        </small>
                                    </div>
                                {% endif %}

                                <div class="item-footer">
                                    <small class="text-muted">Added {{ item.created_at|timesince }} ago</small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Wishlist pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1&type={{ current_type }}">&laquo; First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}&type={{ current_type }}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}&type={{ current_type }}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&type={{ current_type }}">Last &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Your Wishlist is Empty</h3>
                    <p>Start adding pets, products, and services you're interested in!</p>
                    <div class="empty-actions">
                        <a href="{% url 'pet-list' %}" class="btn btn-primary">Browse Pets</a>
                        <a href="{% url 'product-list' %}" class="btn btn-outline-primary">Shop Products</a>
                        <a href="{% url 'services-home' %}" class="btn btn-outline-primary">Find Services</a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function removeFromWishlist(itemId, itemType) {
    if (!confirm('Remove this item from your wishlist?')) {
        return;
    }

    fetch('{% url "toggle-wishlist" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `item_id=${itemId}&item_type=${itemType}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the card from the page
            const card = document.querySelector(`[data-item-id="${itemId}"][data-item-type="${itemType}"]`);
            if (card) {
                card.remove();
            }

            // Check if page is now empty
            const remainingCards = document.querySelectorAll('.wishlist-card');
            if (remainingCards.length === 0) {
                location.reload(); // Reload to show empty state
            }
        } else {
            alert('Error removing item from wishlist: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing the item.');
    });
}
</script>

{% csrf_token %}

<style>
.wishlist-filters {
    margin-bottom: 2rem;
}

.nav-tabs {
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
    border: none;
    color: var(--text-secondary);
    padding: 1rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: var(--primary-color);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-color: transparent transparent var(--primary-color) transparent;
    border-bottom-width: 2px;
}

.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.wishlist-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.wishlist-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.wishlist-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--background-light);
    border-bottom: 1px solid var(--border-color);
}

.item-type-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.remove-from-wishlist {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.remove-from-wishlist:hover {
    background: var(--danger-color);
    color: white;
}

.wishlist-item-content {
    padding: 1.5rem;
}

.item-image {
    margin-bottom: 1rem;
}

.wishlist-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.item-details {
    flex: 1;
}

.item-title {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.item-title a {
    color: var(--text-primary);
    text-decoration: none;
}

.item-title a:hover {
    color: var(--primary-color);
}

.item-meta {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.item-owner, .service-provider {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.adoption-badge {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.adoption-price, .item-price, .service-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.original-price {
    text-decoration: line-through;
    color: var(--text-secondary);
    margin-right: 0.5rem;
}

.discount-price {
    color: var(--danger-color);
    font-weight: 600;
}

.out-of-stock {
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}

.service-item {
    padding: 1rem 0;
}

.service-duration {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.item-notes {
    margin-top: 1rem;
    padding: 0.75rem;
    background: var(--background-light);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

.item-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .wishlist-grid {
        grid-template-columns: 1fr;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>
{% endblock %}
