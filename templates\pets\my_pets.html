{% extends 'base.html' %}
{% load static %}

{% block title %}My Pets - PetPaw{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
        padding: var(--spacing-3xl) 0;
        margin-bottom: var(--spacing-3xl);
        border-radius: var(--border-radius);
    }

    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-3xl);
    }

    .stat-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-xl);
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: var(--font-3xl);
        font-weight: 700;
        color: var(--primary-color);
        display: block;
        margin-bottom: var(--spacing-sm);
    }

    .stat-label {
        color: var(--text-secondary);
        font-weight: 500;
    }

    .alerts-section {
        margin-bottom: var(--spacing-3xl);
    }

    .alert-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-left: 4px solid var(--warning-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-base);
    }

    .alert-card.danger {
        border-left-color: var(--danger-color);
    }

    .alert-card.info {
        border-left-color: var(--info-color);
    }

    .pets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--spacing-xl);
    }

    .pet-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .pet-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .pet-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .pet-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .adoption-badge {
        position: absolute;
        top: var(--spacing-base);
        right: var(--spacing-base);
        background: var(--success-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-size: var(--font-sm);
        font-weight: 500;
    }

    .pet-content {
        padding: var(--spacing-lg);
    }

    .pet-name {
        font-size: var(--font-xl);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
    }

    .pet-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
        font-size: var(--font-sm);
        color: var(--text-secondary);
    }

    .pet-actions {
        display: flex;
        gap: var(--spacing-sm);
    }

    .btn-sm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-sm);
    }

    .quick-actions {
        display: flex;
        gap: var(--spacing-base);
        margin-bottom: var(--spacing-3xl);
        flex-wrap: wrap;
    }

    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl) var(--spacing-xl);
        color: var(--text-secondary);
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="dashboard-header">
        <div class="text-center">
            <h1 class="page-title">My Pets Dashboard</h1>
            <p class="text-muted">Manage and track all your beloved pets</p>
        </div>
    </div>

    <!-- Dashboard Statistics -->
    <div class="dashboard-stats">
        <div class="stat-card">
            <span class="stat-number">{{ total_pets }}</span>
            <span class="stat-label">Total Pets</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ pets_for_adoption }}</span>
            <span class="stat-label">Available for Adoption</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ upcoming_reminders|length }}</span>
            <span class="stat-label">Upcoming Reminders</span>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ overdue_vaccinations|length }}</span>
            <span class="stat-label">Overdue Vaccinations</span>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'pet-create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Pet
        </a>
        <a href="#" class="btn btn-outline-primary">
            <i class="fas fa-calendar"></i> Schedule Reminder
        </a>
        <a href="#" class="btn btn-outline-primary">
            <i class="fas fa-syringe"></i> Log Vaccination
        </a>
        <a href="#" class="btn btn-outline-primary">
            <i class="fas fa-weight"></i> Record Weight
        </a>
    </div>

    <!-- Alerts Section -->
    {% if overdue_vaccinations or upcoming_reminders %}
        <div class="alerts-section">
            <h3>Important Alerts</h3>
            
            {% for vaccination in overdue_vaccinations %}
                <div class="alert-card danger">
                    <strong>Overdue Vaccination:</strong> 
                    {{ vaccination.pet.name }} needs {{ vaccination.get_vaccine_type_display }} 
                    (Due: {{ vaccination.next_due_date }})
                </div>
            {% endfor %}
            
            {% for reminder in upcoming_reminders %}
                <div class="alert-card {% if reminder.is_overdue %}danger{% else %}info{% endif %}">
                    <strong>{% if reminder.is_overdue %}Overdue{% else %}Upcoming{% endif %} Reminder:</strong> 
                    {{ reminder.title }} for {{ reminder.pet.name }} 
                    (Due: {{ reminder.due_date|date:"M d, Y" }})
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Pets Grid -->
    <div class="section-header">
        <h3>My Pets</h3>
    </div>

    {% if pets %}
        <div class="pets-grid">
            {% for pet in pets %}
                <div class="pet-card">
                    <div class="pet-image">
                        <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}">
                        {% if pet.is_for_adoption %}
                            <div class="adoption-badge">
                                <i class="fas fa-heart"></i> For Adoption
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="pet-content">
                        <h4 class="pet-name">{{ pet.name }}</h4>
                        
                        <div class="pet-info">
                            <div><i class="fas fa-paw"></i> {{ pet.category.name }}</div>
                            <div><i class="fas fa-venus-mars"></i> {{ pet.get_gender_display }}</div>
                            <div><i class="fas fa-birthday-cake"></i> {{ pet.get_age }}</div>
                            {% if pet.current_weight %}
                                <div><i class="fas fa-weight"></i> {{ pet.current_weight }}kg</div>
                            {% endif %}
                        </div>
                        
                        <div class="pet-actions">
                            <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-primary btn-sm">
                                View Details
                            </a>
                            <a href="{% url 'pet-update' pk=pet.pk %}" class="btn btn-outline-secondary btn-sm">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <nav aria-label="Pets pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">&laquo; First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-paw"></i>
            </div>
            <h3>No Pets Yet</h3>
            <p>Start by adding your first pet to track their health, growth, and care.</p>
            <a href="{% url 'pet-create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Your First Pet
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
