{% extends 'base.html' %}

{% block title %}Notifications | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .notifications-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }

    .notifications-header {
        margin-bottom: var(--spacing-xl);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notifications-title {
        font-size: var(--font-2xl);
    }

    .notifications-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    .notification-item {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: var(--spacing-base);
        transition: var(--transition-base);
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-item:hover {
        background-color: var(--gray-50);
    }

    .notification-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .notification-content {
        flex: 1;
    }

    .notification-message {
        margin-bottom: var(--spacing-xs);
    }

    .notification-message a {
        font-weight: var(--fw-medium);
        color: var(--text);
        text-decoration: none;
    }

    .notification-message a:hover {
        color: var(--primary);
    }

    .notification-time {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background-color: var(--gray-100);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text);
    }

    .notification-icon.message {
        background-color: rgba(var(--info-rgb), 0.1);
        color: var(--info);
    }

    .notification-icon.pet_inquiry {
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
    }

    .notification-icon.pet_inquiry_response {
        background-color: rgba(var(--success-rgb), 0.1);
        color: var(--success);
    }

    .notification-unread {
        width: 10px;
        height: 10px;
        border-radius: var(--radius-full);
        background-color: var(--primary);
    }

    /* Enhanced notification styles */
    .notification-item {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        background-color: var(--gray-50);
        transform: translateX(5px);
    }

    .notification-item.unread {
        background-color: rgba(var(--primary-rgb), 0.05);
        border-left: 4px solid var(--primary);
    }

    .notification-item.unread .notification-message {
        font-weight: var(--fw-medium);
    }

    .notification-item.read {
        opacity: 0.8;
    }

    .notification-link {
        transition: color 0.3s ease;
    }

    .notification-link:hover {
        color: var(--primary) !important;
    }

    .empty-state {
        padding: var(--spacing-2xl);
        text-align: center;
    }

    .empty-state p {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="notifications-container">
        <div class="notifications-header">
            <h1 class="notifications-title">Notifications</h1>

            <a href="{% url 'inbox' %}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Messages
            </a>
        </div>

        <div class="notifications-card">
            {% for notification in notifications %}
                <div class="notification-item {% if not notification.is_read %}unread{% endif %}" data-notification-id="{{ notification.id }}">
                    <img src="{{ notification.sender.profile_picture.url }}" alt="{{ notification.sender.full_name|default:notification.sender.email }}" class="notification-avatar">

                    <div class="notification-content">
                        <div class="notification-message">
                            {% if notification.notification_type == 'message' %}
                                <a href="{% url 'user-profile' user_id=notification.sender.id %}" class="notification-link" data-url="{% url 'conversation-detail' pk=notification.conversation.pk %}">{{ notification.sender.full_name|default:notification.sender.email }}</a> sent you a <a href="{% url 'conversation-detail' pk=notification.conversation.pk %}" class="notification-link">message</a>.
                            {% elif notification.notification_type == 'pet_inquiry' %}
                                <a href="{% url 'user-profile' user_id=notification.sender.id %}" class="notification-link">{{ notification.sender.full_name|default:notification.sender.email }}</a> is interested in your pet <a href="{% url 'pet-detail' pk=notification.pet.pk %}" class="notification-link">{{ notification.pet.name }}</a>.
                            {% elif notification.notification_type == 'pet_inquiry_response' %}
                                <a href="{% url 'user-profile' user_id=notification.sender.id %}" class="notification-link">{{ notification.sender.full_name|default:notification.sender.email }}</a> responded to your pet inquiry for <a href="{% url 'pet-detail' pk=notification.pet.pk %}" class="notification-link">{{ notification.pet.name }}</a>.
                            {% else %}
                                {{ notification.message }}
                            {% endif %}
                        </div>
                        <div class="notification-time">{{ notification.created_at|timesince }} ago</div>
                    </div>

                    <div class="notification-icon {{ notification.notification_type }}">
                        {% if notification.notification_type == 'message' %}
                            <i class="fas fa-envelope"></i>
                        {% elif notification.notification_type == 'pet_inquiry' %}
                            <i class="fas fa-paw"></i>
                        {% elif notification.notification_type == 'pet_inquiry_response' %}
                            <i class="fas fa-reply"></i>
                        {% endif %}
                    </div>

                    {% if not notification.is_read %}
                        <div class="notification-unread"></div>
                    {% endif %}
                </div>
            {% empty %}
                <div class="empty-state">
                    <p>You don't have any notifications yet.</p>
                    <p>Start messaging with other users to receive notifications.</p>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Don't automatically mark all as read - let user interact with them

    // Handle notification clicks
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Don't trigger if clicking on a link directly
            if (e.target.tagName === 'A') {
                handleNotificationLinkClick(this, e.target.href);
                return;
            }

            // Find the first link in the notification and navigate to it
            const firstLink = this.querySelector('.notification-link');
            if (firstLink) {
                handleNotificationLinkClick(this, firstLink.href);
            }
        });
    });

    // Handle direct link clicks within notifications
    const notificationLinks = document.querySelectorAll('.notification-link');
    notificationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const notificationItem = this.closest('.notification-item');
            handleNotificationLinkClick(notificationItem, this.href);
        });
    });

    function handleNotificationLinkClick(notificationItem, targetUrl) {
        const notificationId = notificationItem.getAttribute('data-notification-id');

        // Mark notification as read
        markNotificationAsRead(notificationId, function() {
            // Update UI immediately
            notificationItem.classList.remove('unread');
            notificationItem.classList.add('read');
            const unreadDot = notificationItem.querySelector('.notification-unread');
            if (unreadDot) {
                unreadDot.remove();
            }

            // Update badge count
            updateNotificationBadge();

            // Navigate to the target URL
            window.location.href = targetUrl;
        });
    }

    function markNotificationAsRead(notificationId, callback) {
        fetch('{% url "notifications" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': getCookie('csrftoken'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `notification_id=${notificationId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && callback) {
                callback();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
            // Still execute callback to provide good UX
            if (callback) callback();
        });
    }

    function markAllNotificationsAsRead() {
        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
        if (unreadNotifications.length === 0) return;

        // Mark all unread notifications as read
        unreadNotifications.forEach(item => {
            const notificationId = item.getAttribute('data-notification-id');
            markNotificationAsRead(notificationId);
        });

        // Update badge after a short delay
        setTimeout(updateNotificationBadge, 500);
    }

    function updateNotificationBadge() {
        // Count remaining unread notifications on the page
        const unreadCount = document.querySelectorAll('.notification-item.unread').length;

        // Use the global function to update the badge
        if (window.updateNotificationBadge) {
            window.updateNotificationBadge(unreadCount);
        }

        // Also refresh from server to ensure accuracy
        if (window.refreshNotificationCount) {
            window.refreshNotificationCount();
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
</script>
{% endblock %}
