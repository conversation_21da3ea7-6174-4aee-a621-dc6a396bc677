{% extends 'base.html' %}

{% block title %}{{ product.name }} | PetPaw Shop{% endblock %}

{% block extra_css %}
<style>
    .product-detail-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-2xl);
    }

    @media (max-width: 992px) {
        .product-detail-container {
            grid-template-columns: 1fr;
        }
    }

    .product-gallery {
        position: relative;
    }

    .product-main-image {
        width: 100%;
        height: 400px;
        object-fit: contain;
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-base);
        background-color: var(--white);
        padding: var(--spacing-base);
    }

    .product-thumbnails {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--gap-sm);
    }

    .product-thumbnail {
        width: 100%;
        height: 80px;
        object-fit: cover;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: var(--transition-base);
        background-color: var(--white);
        padding: var(--spacing-xs);
    }

    .product-thumbnail:hover {
        opacity: 0.8;
    }

    .product-thumbnail.active {
        border: 2px solid var(--primary);
    }

    .product-info {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }

    .product-category {
        color: var(--text-light);
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-xs);
    }

    .product-name {
        font-size: var(--font-3xl);
        margin-bottom: var(--spacing-base);
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-lg);
    }

    .rating-stars {
        color: var(--warning);
    }

    .rating-count {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    .product-price {
        font-size: var(--font-2xl);
        font-weight: var(--fw-bold);
        color: var(--primary);
        margin-bottom: var(--spacing-xl);
    }

    .product-description {
        margin-bottom: var(--spacing-xl);
        line-height: 1.6;
    }

    .product-meta {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--gap-base);
        margin-bottom: var(--spacing-xl);
        padding-bottom: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .product-meta-item {
        display: flex;
        flex-direction: column;
    }

    .meta-label {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-xs);
    }

    .meta-value {
        font-weight: var(--fw-medium);
    }

    .product-form {
        margin-bottom: var(--spacing-xl);
    }

    .quantity-control {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-lg);
    }

    .quantity-label {
        margin-right: var(--spacing-base);
        font-weight: var(--fw-medium);
    }

    .quantity-input-group {
        display: flex;
        align-items: center;
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        overflow: hidden;
    }

    .quantity-btn {
        background: none;
        border: none;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: var(--font-lg);
        color: var(--text);
        transition: var(--transition-base);
    }

    .quantity-btn:hover {
        background-color: var(--gray-100);
    }

    .quantity-input {
        width: 60px;
        height: 40px;
        border: none;
        border-left: 1px solid var(--gray-300);
        border-right: 1px solid var(--gray-300);
        text-align: center;
        font-size: var(--font-base);
    }

    .product-actions {
        display: flex;
        gap: var(--gap-base);
        flex-wrap: wrap;
    }

    .add-to-cart-btn {
        flex: 1;
        min-width: 150px;
    }

    .buy-now-btn {
        flex: 1;
        min-width: 150px;
    }

    /* Wishlist Button Styles */
    .btn-wishlist {
        background-color: transparent;
        border: 2px solid var(--primary);
        color: var(--primary);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        min-width: 150px;
        justify-content: center;
    }

    .btn-wishlist:hover {
        background-color: var(--primary);
        color: white;
        transform: translateY(-2px);
    }

    .btn-wishlist.in-wishlist {
        background-color: var(--primary);
        color: white;
    }

    .btn-wishlist.in-wishlist:hover {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-wishlist i {
        transition: transform 0.2s ease;
    }

    .btn-wishlist:hover i {
        transform: scale(1.1);
    }

    .product-tabs {
        margin-top: var(--spacing-3xl);
    }

    .tab-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: var(--spacing-xl);
    }

    .tab-item {
        margin-right: var(--spacing-xl);
    }

    .tab-link {
        display: block;
        padding: var(--spacing-base) 0;
        color: var(--text-light);
        font-weight: var(--fw-medium);
        position: relative;
    }

    .tab-link.active {
        color: var(--primary);
    }

    .tab-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary);
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    .product-reviews {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    .review {
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .review:last-child {
        border-bottom: none;
    }

    .review-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-base);
    }

    .reviewer {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .reviewer-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .reviewer-info {
        display: flex;
        flex-direction: column;
    }

    .reviewer-name {
        font-weight: var(--fw-medium);
    }

    .review-date {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .review-rating {
        color: var(--warning);
    }

    .review-content {
        margin-bottom: var(--spacing-base);
    }

    .review-form-container {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        margin-top: var(--spacing-xl);
    }

    .review-form-title {
        margin-bottom: var(--spacing-lg);
    }

    .star-rating {
        display: flex;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }

    .star-rating input {
        display: none;
    }

    .star-rating label {
        cursor: pointer;
        font-size: var(--font-xl);
        color: var(--gray-300);
    }

    .star-rating label:hover,
    .star-rating label:hover ~ label,
    .star-rating input:checked ~ label {
        color: var(--warning);
    }

    .related-products {
        margin-top: var(--spacing-3xl);
    }

    .related-products-title {
        margin-bottom: var(--spacing-xl);
    }

    .related-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--gap-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="product-detail-container">
        <div class="product-gallery">
            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-main-image" id="main-image">

            <div class="product-thumbnails">
                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-thumbnail active" data-src="{{ product.image.url }}">

                {% for image in product.additional_images.all %}
                    <img src="{{ image.image.url }}" alt="{{ product.name }}" class="product-thumbnail" data-src="{{ image.image.url }}">
                {% endfor %}
            </div>
        </div>

        <div class="product-info">
            <div class="product-category">{{ product.category.name }}</div>
            <h1 class="product-name">{{ product.name }}</h1>

            <div class="product-rating">
                <div class="rating-stars">
                    {% for i in "12345" %}
                        {% if forloop.counter <= product.average_rating %}
                            <i class="fas fa-star"></i>
                        {% elif forloop.counter <= product.average_rating|add:0.5 %}
                            <i class="fas fa-star-half-alt"></i>
                        {% else %}
                            <i class="far fa-star"></i>
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="rating-count">{{ product.reviews.count }} reviews</div>
            </div>

            <div class="product-price">${{ product.price }}</div>

            <div class="product-description">
                {{ product.description }}
            </div>

            <div class="product-meta">
                <div class="product-meta-item">
                    <div class="meta-label">SKU</div>
                    <div class="meta-value">{{ product.sku }}</div>
                </div>

                <div class="product-meta-item">
                    <div class="meta-label">Availability</div>
                    <div class="meta-value">
                        {% if product.stock > 0 %}
                            <span class="text-success">In Stock ({{ product.stock }})</span>
                        {% else %}
                            <span class="text-danger">Out of Stock</span>
                        {% endif %}
                    </div>
                </div>

                <div class="product-meta-item">
                    <div class="meta-label">For Pet Type</div>
                    <div class="meta-value">{{ product.pet_category.name }}</div>
                </div>

                <div class="product-meta-item">
                    <div class="meta-label">Brand</div>
                    <div class="meta-value">{{ product.brand }}</div>
                </div>
            </div>

            <form method="post" action="{% url 'add-to-cart' pk=product.pk %}" class="product-form">
                {% csrf_token %}

                <div class="quantity-control">
                    <div class="quantity-label">Quantity:</div>
                    <div class="quantity-input-group">
                        <button type="button" class="quantity-btn decrement-btn">-</button>
                        <input type="number" name="quantity" value="1" min="1" max="{{ product.stock }}" class="quantity-input">
                        <button type="button" class="quantity-btn increment-btn">+</button>
                    </div>
                </div>

                <div class="product-actions">
                    <button type="submit" class="btn btn-primary add-to-cart-btn">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                    <button type="button" class="btn btn-secondary buy-now-btn">
                        <i class="fas fa-bolt"></i> Buy Now
                    </button>
                    {% if user.is_authenticated %}
                        <button type="button" class="btn btn-wishlist" id="wishlist-btn" data-item-id="{{ product.id }}" data-item-type="product">
                            <i class="fas fa-heart"></i>
                            <span class="wishlist-text">Add to Wishlist</span>
                        </button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <div class="product-tabs">
        <ul class="tab-list">
            <li class="tab-item">
                <a href="#description" class="tab-link active">Description</a>
            </li>
            <li class="tab-item">
                <a href="#specifications" class="tab-link">Specifications</a>
            </li>
            <li class="tab-item">
                <a href="#reviews" class="tab-link">Reviews ({{ product.reviews.count }})</a>
            </li>
        </ul>

        <div class="tab-content">
            <div id="description" class="tab-pane active">
                <div class="product-description-content">
                    {{ product.description|linebreaks }}
                </div>
            </div>

            <div id="specifications" class="tab-pane">
                <div class="product-specifications">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>Brand</th>
                                <td>{{ product.brand }}</td>
                            </tr>
                            <tr>
                                <th>Weight</th>
                                <td>{{ product.weight }} {{ product.weight_unit }}</td>
                            </tr>
                            <tr>
                                <th>Dimensions</th>
                                <td>{{ product.dimensions }}</td>
                            </tr>
                            <tr>
                                <th>Material</th>
                                <td>{{ product.material }}</td>
                            </tr>
                            <tr>
                                <th>Country of Origin</th>
                                <td>{{ product.country_of_origin }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div id="reviews" class="tab-pane">
                <div class="product-reviews">
                    {% for review in reviews %}
                        <div class="review">
                            <div class="review-header">
                                <div class="reviewer">
                                    <img src="{{ review.user.profile_picture.url }}" alt="{{ review.user.username }}" class="reviewer-avatar">
                                    <div class="reviewer-info">
                                        <div class="reviewer-name">{{ review.user.username }}</div>
                                        <div class="review-date">{{ review.created_at|date:"F j, Y" }}</div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="review-content">
                                {{ review.comment }}
                            </div>
                        </div>
                    {% empty %}
                        <div class="empty-state">
                            <p>No reviews yet. Be the first to review this product!</p>
                        </div>
                    {% endfor %}
                </div>

                {% if user.is_authenticated %}
                    <div class="review-form-container">
                        <h3 class="review-form-title">Write a Review</h3>
                        <form method="post" action="{% url 'add-review' pk=product.pk %}">
                            {% csrf_token %}

                            <div class="form-group">
                                <label class="form-label">Rating</label>
                                <div class="star-rating">
                                    <input type="radio" id="star5" name="rating" value="5" required>
                                    <label for="star5"><i class="fas fa-star"></i></label>
                                    <input type="radio" id="star4" name="rating" value="4">
                                    <label for="star4"><i class="fas fa-star"></i></label>
                                    <input type="radio" id="star3" name="rating" value="3">
                                    <label for="star3"><i class="fas fa-star"></i></label>
                                    <input type="radio" id="star2" name="rating" value="2">
                                    <label for="star2"><i class="fas fa-star"></i></label>
                                    <input type="radio" id="star1" name="rating" value="1">
                                    <label for="star1"><i class="fas fa-star"></i></label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="comment" class="form-label">Your Review</label>
                                <textarea name="comment" id="comment" rows="4" class="form-control" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">Submit Review</button>
                        </form>
                    </div>
                {% else %}
                    <div class="review-login-prompt">
                        <p>Please <a href="{% url 'account_login' %}">log in</a> to write a review.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="related-products">
        <h2 class="related-products-title">Related Products</h2>

        <div class="related-products-grid">
            {% for related in related_products %}
                <div class="product-card">
                    <div class="product-card-image">
                        <img src="{{ related.image.url }}" alt="{{ related.name }}">
                        <div class="product-card-actions">
                            <a href="{% url 'product-detail' pk=related.pk %}" class="product-action-button">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button type="button" class="product-action-button add-to-cart-btn" data-url="{% url 'add-to-cart' pk=related.pk %}">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-card-content">
                        <h3 class="product-card-title">{{ related.name }}</h3>
                        <div class="product-card-category">{{ related.category.name }}</div>
                        <div class="product-card-price">${{ related.price }}</div>
                        <div class="product-card-rating">
                            {% for i in "12345" %}
                                {% if forloop.counter <= related.average_rating %}
                                    <i class="fas fa-star"></i>
                                {% elif forloop.counter <= related.average_rating|add:0.5 %}
                                    <i class="fas fa-star-half-alt"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                            <span>({{ related.reviews.count }})</span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image gallery
        const mainImage = document.getElementById('main-image');
        const thumbnails = document.querySelectorAll('.product-thumbnail');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Update main image
                mainImage.src = this.getAttribute('data-src');

                // Update active thumbnail
                thumbnails.forEach(thumb => thumb.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Tabs
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                tabLinks.forEach(tab => tab.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding tab content
                const target = this.getAttribute('href').substring(1);
                document.getElementById(target).classList.add('active');
            });
        });

        // Quantity increment/decrement
        const quantityInput = document.querySelector('.quantity-input');
        const decrementBtn = document.querySelector('.decrement-btn');
        const incrementBtn = document.querySelector('.increment-btn');

        if (quantityInput && decrementBtn && incrementBtn) {
            decrementBtn.addEventListener('click', function() {
                let value = parseInt(quantityInput.value);
                if (value > 1) {
                    quantityInput.value = value - 1;
                }
            });

            incrementBtn.addEventListener('click', function() {
                let value = parseInt(quantityInput.value);
                let max = parseInt(quantityInput.getAttribute('max'));

                if (value < max) {
                    quantityInput.value = value + 1;
                }
            });
        }

        // Buy Now button
        const buyNowBtn = document.querySelector('.buy-now-btn');

        if (buyNowBtn) {
            buyNowBtn.addEventListener('click', function() {
                // Submit the form and redirect to checkout
                const form = document.querySelector('.product-form');

                if (form) {
                    // Add a hidden input to indicate "buy now"
                    const buyNowInput = document.createElement('input');
                    buyNowInput.type = 'hidden';
                    buyNowInput.name = 'buy_now';
                    buyNowInput.value = 'true';

                    form.appendChild(buyNowInput);
                    form.submit();
                }
            });
        }

        // Wishlist functionality
        const wishlistBtn = document.getElementById('wishlist-btn');
        if (wishlistBtn) {
            // Check if item is already in wishlist
            checkWishlistStatus();

            wishlistBtn.addEventListener('click', function() {
                const itemId = this.getAttribute('data-item-id');
                const itemType = this.getAttribute('data-item-type');

                // Disable button during request
                this.disabled = true;

                fetch('{% url "toggle-wishlist" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: `item_id=${itemId}&item_type=${itemType}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateWishlistButton(data.in_wishlist);
                    } else {
                        alert('Error: ' + data.error);
                    }
                    this.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating wishlist.');
                    this.disabled = false;
                });
            });
        }

        function checkWishlistStatus() {
            // Get initial status from backend
            const inWishlist = {% if in_wishlist %}true{% else %}false{% endif %};
            updateWishlistButton(inWishlist);
        }

        function updateWishlistButton(inWishlist) {
            const wishlistBtn = document.getElementById('wishlist-btn');
            const wishlistText = wishlistBtn.querySelector('.wishlist-text');
            const icon = wishlistBtn.querySelector('i');

            if (inWishlist) {
                wishlistBtn.classList.add('in-wishlist');
                wishlistText.textContent = 'Remove from Wishlist';
                icon.className = 'fas fa-heart-broken';
            } else {
                wishlistBtn.classList.remove('in-wishlist');
                wishlistText.textContent = 'Add to Wishlist';
                icon.className = 'fas fa-heart';
            }
        }

        // CSRF token helper function
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
