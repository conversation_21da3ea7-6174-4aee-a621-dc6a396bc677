{% extends 'base.html' %}
{% load static %}

{% block title %}My Activity Feed - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="profile-header">
                <h1 class="page-title">My Activity Feed</h1>
                <p class="text-muted">Track your recent activities on PetPaw</p>
            </div>

            {% if activities %}
                <div class="activity-feed">
                    {% for activity in activities %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                {% if activity.activity_type == 'pet_added' %}
                                    <i class="fas fa-plus-circle text-success"></i>
                                {% elif activity.activity_type == 'pet_updated' %}
                                    <i class="fas fa-edit text-info"></i>
                                {% elif activity.activity_type == 'review_written' %}
                                    <i class="fas fa-star text-warning"></i>
                                {% elif activity.activity_type == 'service_booked' %}
                                    <i class="fas fa-calendar-check text-primary"></i>
                                {% elif activity.activity_type == 'order_placed' %}
                                    <i class="fas fa-shopping-cart text-success"></i>
                                {% elif activity.activity_type == 'profile_updated' %}
                                    <i class="fas fa-user-edit text-info"></i>
                                {% elif activity.activity_type == 'became_provider' %}
                                    <i class="fas fa-briefcase text-primary"></i>
                                {% elif activity.activity_type == 'service_added' %}
                                    <i class="fas fa-plus text-success"></i>
                                {% elif activity.activity_type == 'pet_followed' %}
                                    <i class="fas fa-heart text-danger"></i>
                                {% elif activity.activity_type == 'user_followed' %}
                                    <i class="fas fa-user-plus text-primary"></i>
                                {% else %}
                                    <i class="fas fa-circle text-secondary"></i>
                                {% endif %}
                            </div>
                            
                            <div class="activity-content">
                                <div class="activity-description">
                                    {{ activity.description }}
                                </div>
                                <div class="activity-meta">
                                    <span class="activity-type">{{ activity.get_activity_type_display }}</span>
                                    <span class="activity-time">{{ activity.created_at|timesince }} ago</span>
                                    {% if not activity.is_public %}
                                        <span class="badge badge-secondary">Private</span>
                                    {% endif %}
                                </div>
                                
                                {% if activity.content_object %}
                                    <div class="activity-object">
                                        {% if activity.content_type.model == 'pet' %}
                                            <a href="{{ activity.content_object.get_absolute_url }}" class="activity-link">
                                                <img src="{{ activity.content_object.profile_picture.url }}" alt="{{ activity.content_object.name }}" class="activity-thumbnail">
                                                <span>{{ activity.content_object.name }}</span>
                                            </a>
                                        {% elif activity.content_type.model == 'product' %}
                                            <a href="{{ activity.content_object.get_absolute_url }}" class="activity-link">
                                                <img src="{{ activity.content_object.image.url }}" alt="{{ activity.content_object.name }}" class="activity-thumbnail">
                                                <span>{{ activity.content_object.name }}</span>
                                            </a>
                                        {% elif activity.content_type.model == 'service' %}
                                            <a href="{{ activity.content_object.get_absolute_url }}" class="activity-link">
                                                <span>{{ activity.content_object.name }}</span>
                                            </a>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Activity pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">&laquo; First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3>No Activities Yet</h3>
                    <p>Start exploring PetPaw to see your activities here!</p>
                    <div class="empty-actions">
                        <a href="{% url 'pet-list' %}" class="btn btn-primary">Browse Pets</a>
                        <a href="{% url 'product-list' %}" class="btn btn-outline-primary">Shop Products</a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.activity-feed {
    margin-top: 2rem;
}

.activity-item {
    display: flex;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: var(--card-background);
    transition: box-shadow 0.2s ease;
}

.activity-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    margin-right: 1rem;
    font-size: 1.5rem;
    width: 2rem;
    text-align: center;
}

.activity-content {
    flex: 1;
}

.activity-description {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.activity-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.activity-object {
    margin-top: 0.5rem;
}

.activity-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 500;
}

.activity-link:hover {
    text-decoration: underline;
}

.activity-thumbnail {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}
