# Generated by Django 4.2.7 on 2025-05-27 18:48

from django.db import migrations
import uuid


def populate_phone_numbers(apps, schema_editor):
    """
    Populate phone numbers for existing users who don't have them.
    This is needed before making phone_number unique.
    """
    User = apps.get_model('users', 'User')

    # Get all users without phone numbers
    users_without_phone = User.objects.filter(phone_number__in=['', None])

    for i, user in enumerate(users_without_phone):
        # Generate a temporary unique phone number
        # Format: temp_XXXXX where XXXXX is a unique number
        temp_phone = f"temp_{str(uuid.uuid4().int)[:8]}"
        user.phone_number = temp_phone
        user.profile_completed = False  # Mark as incomplete so they'll be prompted
        user.save()


def reverse_populate_phone_numbers(apps, schema_editor):
    """
    Reverse the phone number population by clearing temporary phone numbers.
    """
    User = apps.get_model('users', 'User')

    # Clear temporary phone numbers
    User.objects.filter(phone_number__startswith='temp_').update(phone_number='')


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_make_email_unique'),
    ]

    operations = [
        migrations.RunPython(populate_phone_numbers, reverse_populate_phone_numbers),
    ]
