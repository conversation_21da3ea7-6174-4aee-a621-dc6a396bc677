from django.urls import path
from . import views

urlpatterns = [
    path('', views.PetListView.as_view(), name='pet-list'),
    path('my-pets/', views.MyPetsView.as_view(), name='my-pets'),
    path('<int:pk>/', views.PetDetailView.as_view(), name='pet-detail'),
    path('new/', views.PetCreateView.as_view(), name='pet-create'),
    path('<int:pk>/update/', views.PetUpdateView.as_view(), name='pet-update'),
    path('<int:pk>/delete/', views.PetDeleteView.as_view(), name='pet-delete'),
    path('<int:pk>/follow/', views.follow_pet, name='follow-pet'),
    path('<int:pk>/add-photo/', views.add_pet_photo, name='add-pet-photo'),
    path('<int:pk>/add-medical-record/', views.add_medical_record, name='add-medical-record'),

    # Health tracking URLs
    path('<int:pk>/add-vaccination/', views.add_vaccination, name='add-vaccination'),
    path('<int:pk>/add-growth-record/', views.add_growth_record, name='add-growth-record'),
    path('<int:pk>/add-milestone/', views.add_milestone, name='add-milestone'),
    path('<int:pk>/add-reminder/', views.add_reminder, name='add-reminder'),
    path('<int:pk>/add-exercise-log/', views.add_exercise_log, name='add-exercise-log'),
    path('<int:pk>/add-feeding-log/', views.add_feeding_log, name='add-feeding-log'),

    # AJAX URLs
    path('<int:pk>/quick-weight-log/', views.quick_weight_log, name='quick-weight-log'),
    path('<int:pk>/complete-reminder/<int:reminder_id>/', views.complete_reminder, name='complete-reminder'),
    path('ajax/load-breeds/', views.load_breeds, name='ajax-load-breeds'),
]
