from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import DetailView, UpdateView, ListView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import User, UserProfile, Address, UserActivity, Wishlist, UserPreferences
from .forms import UserProfileForm, ExtendedProfileForm, AddressForm
from .utils import (
    create_user_activity, add_to_wishlist, remove_from_wishlist,
    is_in_wishlist, get_user_activity_feed, get_user_wishlist
)


class UserProfileView(DetailView):
    """View for displaying user profile"""
    model = User
    template_name = 'users/profile.html'
    context_object_name = 'profile_user'

    def get_object(self):
        return get_object_or_404(User, username=self.kwargs.get('username'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()

        # Get user's pets
        context['pets'] = user.pets.all()

        # Check if current user is following the profile user
        context['is_following'] = False
        if self.request.user.is_authenticated:
            if user.profile.followers.filter(id=self.request.user.id).exists():
                context['is_following'] = True

        # Get user's activity feed (if public or own profile)
        if user == self.request.user or (hasattr(user, 'preferences') and user.preferences.show_activity_feed):
            context['activities'] = get_user_activity_feed(user, limit=10)
        else:
            context['activities'] = []

        # Get user's public wishlist (if enabled and public or own profile)
        if user == self.request.user or (hasattr(user, 'preferences') and user.preferences.show_wishlist):
            context['wishlist_pets'] = get_user_wishlist(user, item_type='pet')[:5]
            context['wishlist_products'] = get_user_wishlist(user, item_type='product')[:5]
        else:
            context['wishlist_pets'] = []
            context['wishlist_products'] = []

        # Get user roles
        context['user_roles'] = user.roles.all()
        context['primary_role'] = user.primary_role

        return context


@login_required
def edit_profile(request):
    """View for editing user profile"""
    if request.method == 'POST':
        user_form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        profile_form = ExtendedProfileForm(request.POST, instance=request.user.profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('user-profile', username=request.user.username)
    else:
        user_form = UserProfileForm(instance=request.user)
        profile_form = ExtendedProfileForm(instance=request.user.profile)

    return render(request, 'users/edit_profile.html', {
        'user_form': user_form,
        'profile_form': profile_form
    })


@login_required
def follow_user(request, username):
    """View for following/unfollowing users"""
    user_to_follow = get_object_or_404(User, username=username)

    if request.user == user_to_follow:
        messages.error(request, "You cannot follow yourself.")
        return redirect('user-profile', username=username)

    if user_to_follow.profile.followers.filter(id=request.user.id).exists():
        user_to_follow.profile.followers.remove(request.user)
        messages.success(request, f"You have unfollowed {username}.")
    else:
        user_to_follow.profile.followers.add(request.user)
        messages.success(request, f"You are now following {username}.")

    return redirect('user-profile', username=username)


@login_required
def address_list(request):
    """View for listing user addresses"""
    addresses = Address.objects.filter(user=request.user)
    return render(request, 'users/address_list.html', {'addresses': addresses})


@login_required
def add_address(request):
    """View for adding a new address"""
    if request.method == 'POST':
        form = AddressForm(request.POST)
        if form.is_valid():
            address = form.save(commit=False)
            address.user = request.user

            # If this address is set as default, unset any other default of same type
            if address.default:
                Address.objects.filter(
                    user=request.user,
                    address_type=address.address_type,
                    default=True
                ).update(default=False)

            address.save()
            messages.success(request, 'Address added successfully!')
            return redirect('address-list')
    else:
        form = AddressForm()

    return render(request, 'users/address_form.html', {'form': form, 'title': 'Add Address'})


@login_required
def activity_feed(request):
    """View for displaying user's activity feed"""
    activities = UserActivity.objects.filter(user=request.user).order_by('-created_at')

    # Pagination
    paginator = Paginator(activities, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'users/activity_feed.html', {
        'activities': page_obj,
        'page_obj': page_obj
    })


@login_required
def wishlist_view(request):
    """View for displaying user's wishlist"""
    item_type = request.GET.get('type', 'all')

    if item_type == 'all':
        wishlist_items = Wishlist.objects.filter(user=request.user)
    else:
        wishlist_items = Wishlist.objects.filter(user=request.user, item_type=item_type)

    # Pagination
    paginator = Paginator(wishlist_items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'users/wishlist.html', {
        'wishlist_items': page_obj,
        'page_obj': page_obj,
        'current_type': item_type
    })


@login_required
def toggle_wishlist(request):
    """AJAX view for adding/removing items from wishlist"""
    if request.method == 'POST':
        item_type = request.POST.get('item_type')
        item_id = request.POST.get('item_id')

        if not item_type or not item_id:
            return JsonResponse({'success': False, 'error': 'Missing parameters'})

        # Get the appropriate model
        if item_type == 'pet':
            from pets.models import Pet
            try:
                item = Pet.objects.get(id=item_id)
            except Pet.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Pet not found'})
        elif item_type == 'product':
            from shop.models import Product
            try:
                item = Product.objects.get(id=item_id)
            except Product.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Product not found'})
        elif item_type == 'service':
            from services.models import Service
            try:
                item = Service.objects.get(id=item_id)
            except Service.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Service not found'})
        else:
            return JsonResponse({'success': False, 'error': 'Invalid item type'})

        # Check if item is already in wishlist
        if is_in_wishlist(request.user, item):
            # Remove from wishlist
            success = remove_from_wishlist(request.user, item)
            action = 'removed'
        else:
            # Add to wishlist
            wishlist_item = add_to_wishlist(request.user, item, item_type)
            success = wishlist_item is not None
            action = 'added'

        return JsonResponse({
            'success': success,
            'action': action,
            'in_wishlist': is_in_wishlist(request.user, item)
        })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def user_preferences(request):
    """View for managing user preferences"""
    preferences, created = UserPreferences.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update preferences based on form data
        preferences.show_pet_activities = request.POST.get('show_pet_activities') == 'on'
        preferences.show_service_activities = request.POST.get('show_service_activities') == 'on'
        preferences.show_shop_activities = request.POST.get('show_shop_activities') == 'on'
        preferences.show_social_activities = request.POST.get('show_social_activities') == 'on'

        preferences.notify_on_pet_inquiry = request.POST.get('notify_on_pet_inquiry') == 'on'
        preferences.notify_on_service_booking = request.POST.get('notify_on_service_booking') == 'on'
        preferences.notify_on_order_update = request.POST.get('notify_on_order_update') == 'on'
        preferences.notify_on_new_follower = request.POST.get('notify_on_new_follower') == 'on'
        preferences.notify_on_review = request.POST.get('notify_on_review') == 'on'

        preferences.show_activity_feed = request.POST.get('show_activity_feed') == 'on'
        preferences.show_wishlist = request.POST.get('show_wishlist') == 'on'
        preferences.show_pets = request.POST.get('show_pets') == 'on'
        preferences.show_reviews = request.POST.get('show_reviews') == 'on'

        preferences.save()

        # Update user privacy level
        request.user.privacy_level = request.POST.get('privacy_level', 'public')
        request.user.email_notifications = request.POST.get('email_notifications') == 'on'
        request.user.push_notifications = request.POST.get('push_notifications') == 'on'
        request.user.save()

        # Create activity for profile update
        create_user_activity(
            user=request.user,
            activity_type='profile_updated',
            description='Updated privacy and notification preferences',
            is_public=False
        )

        messages.success(request, 'Your preferences have been updated successfully!')
        return redirect('user-preferences')

    return render(request, 'users/preferences.html', {
        'preferences': preferences
    })
