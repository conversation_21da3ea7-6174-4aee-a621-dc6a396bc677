{% extends 'base.html' %}

{% block title %}Log Exercise for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .exercise-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .exercise-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .exercise-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .form-row-three {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row, .form-row-three {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .measurement-input {
        position: relative;
    }
    
    .measurement-input .unit {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-500);
        font-size: var(--font-sm);
        pointer-events: none;
    }
    
    .measurement-input input {
        padding-right: 50px;
    }
    
    .activity-examples {
        background-color: var(--gray-50);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .activity-examples h4 {
        margin-bottom: var(--spacing-md);
        color: var(--gray-700);
    }
    
    .activity-examples ul {
        margin: 0;
        padding-left: var(--spacing-lg);
    }
    
    .activity-examples li {
        margin-bottom: var(--spacing-xs);
        color: var(--gray-600);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="exercise-form-container">
        <div class="exercise-form-header">
            <h1>Log Exercise for {{ pet.name }}</h1>
            <p>Track your pet's physical activities and exercise</p>
        </div>
        
        <div class="exercise-form">
            <form method="post">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Exercise Details</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.date.id_for_label }}" class="form-label">Date</label>
                            {{ form.date }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.activity_type.id_for_label }}" class="form-label">Activity Type</label>
                            {{ form.activity_type }}
                        </div>
                    </div>
                    
                    <div class="form-row-three">
                        <div class="form-group measurement-input">
                            <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">Duration</label>
                            {{ form.duration_minutes }}
                            <span class="unit">minutes</span>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.intensity.id_for_label }}" class="form-label">Intensity</label>
                            {{ form.intensity }}
                        </div>
                        
                        <div class="form-group measurement-input">
                            <label for="{{ form.distance.id_for_label }}" class="form-label">Distance (Optional)</label>
                            {{ form.distance }}
                            <span class="unit">km</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.location.id_for_label }}" class="form-label">Location (Optional)</label>
                        {{ form.location }}
                        <small class="form-text">Where did the activity take place?</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.weather.id_for_label }}" class="form-label">Weather (Optional)</label>
                        {{ form.weather }}
                        <small class="form-text">Weather conditions during the activity</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                        {{ form.notes }}
                        <small class="form-text">How did your pet respond? Any observations?</small>
                    </div>
                </div>
                
                <div class="activity-examples">
                    <h4>Activity Examples:</h4>
                    <ul>
                        <li><strong>Walk:</strong> Regular neighborhood walks, leash training</li>
                        <li><strong>Run:</strong> Jogging, running in the park</li>
                        <li><strong>Play:</strong> Fetch, tug-of-war, interactive toys</li>
                        <li><strong>Training:</strong> Obedience training, trick learning</li>
                        <li><strong>Swimming:</strong> Pool time, beach visits, water therapy</li>
                        <li><strong>Hiking:</strong> Trail walks, nature exploration</li>
                        <li><strong>Agility:</strong> Obstacle courses, agility training</li>
                    </ul>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Log Exercise</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set today's date as default
        const dateInput = document.querySelector('input[name="date"]');
        if (dateInput && !dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
        
        // Auto-suggest duration based on activity type
        const activityTypeSelect = document.querySelector('select[name="activity_type"]');
        const durationInput = document.querySelector('input[name="duration_minutes"]');
        const intensitySelect = document.querySelector('select[name="intensity"]');
        
        if (activityTypeSelect && durationInput) {
            activityTypeSelect.addEventListener('change', function() {
                if (!durationInput.value) { // Only suggest if duration is empty
                    switch (this.value) {
                        case 'walk':
                            durationInput.value = '30';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'moderate';
                            break;
                        case 'run':
                            durationInput.value = '20';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'high';
                            break;
                        case 'play':
                            durationInput.value = '15';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'moderate';
                            break;
                        case 'training':
                            durationInput.value = '10';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'low';
                            break;
                        case 'swimming':
                            durationInput.value = '25';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'high';
                            break;
                        case 'hiking':
                            durationInput.value = '60';
                            if (intensitySelect && !intensitySelect.value) intensitySelect.value = 'moderate';
                            break;
                    }
                }
            });
        }
    });
</script>
{% endblock %}
