:root {
  /* Main Colors */
  --primary: rgb(33, 121, 253);
  --primary-light: rgba(33, 121, 253, 0.2);
  --primary-dark: rgb(80, 150, 255);
  --secondary: #832BB8;
  --secondary-light: rgba(131, 43, 184, 0.2);
  --secondary-dark: #6a1c9e;
  --tertiary: #ff0055;
  --accent: #f3ad15;
  --accent2: #28a745;
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;
  --light: #f8f9fa;
  --dark: #343a40;
  --white: #ffffff;
  --black: #000000;
  --text: #333333;
  --text-light: #6c757d;
  --border-color: #ced4da;
  --background: #f9f9f9;
  --card-bg: #ffffff;

  /* Primary Color Shades */
  --primary-50: #fff1f3;
  --primary-100: #ffe4e7;
  --primary-200: #ffc8cf;
  --primary-300: #ff9da8;
  --primary-400: #ff6b7a;
  --primary-500: #fd2146;
  --primary-600: #e51436;
  --primary-700: #c20d2d;
  --primary-800: #9f0a26;
  --primary-900: #7d081f;

  /* Secondary Color Shades */
  --secondary-50: #f3e5f5;
  --secondary-100: #e1bee7;
  --secondary-200: #ce93d8;
  --secondary-300: #ba68c8;
  --secondary-400: #ab47bc;
  --secondary-500: #9c27b0;
  --secondary-600: #8e24aa;
  --secondary-700: #7b1fa2;
  --secondary-800: #6a1b9a;
  --secondary-900: #4a148c;

  /* Tertiary Color Shades */
  --tertiary-50: #fce4ec;
  --tertiary-100: #f8bbd0;
  --tertiary-200: #f48fb1;
  --tertiary-300: #f06292;
  --tertiary-400: #ec407a;
  --tertiary-500: #e91e63;
  --tertiary-600: #d81b60;
  --tertiary-700: #c2185b;
  --tertiary-800: #ad1457;
  --tertiary-900: #880e4f;

  /* Gray Scale */
  --gray-0: #000000;
  --gray-50: #fafafa;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;

  /* Button Colors */
  /* Primary Button */
  --button-primary-bg: var(--primary);
  --button-primary-text: var(--white);
  --button-primary-hover-bg: var(--primary-dark);

  /* Secondary Button */
  --button-secondary-bg: var(--secondary);
  --button-secondary-text: var(--white);
  --button-secondary-hover-bg: var(--secondary-dark);

  /* Tertiary Button */
  --button-tertiary-bg: var(--tertiary);
  --button-tertiary-text: var(--white);
  --button-tertiary-hover-bg: var(--tertiary-dark, var(--tertiary));

  /* Success Button */
  --button-success-bg: var(--success);
  --button-success-text: var(--white);
  --button-success-hover-bg: var(--success-dark, #1e7e34);

  /* Danger Button */
  --button-danger-bg: var(--danger);
  --button-danger-text: var(--white);
  --button-danger-hover-bg: var(--danger-dark, #bd2130);

  /* Warning Button */
  --button-warning-bg: var(--warning);
  --button-warning-text: var(--dark);
  --button-warning-hover-bg: var(--warning-dark, #d39e00);

  /* Info Button */
  --button-info-bg: var(--info);
  --button-info-text: var(--white);
  --button-info-hover-bg: var(--info-dark, #138496);

  /* Outline Buttons */
  --button-outline-border: var(--primary);
  --button-outline-text: var(--primary);
  --button-outline-hover-bg: var(--primary-light);

  /* Text Buttons */
  --button-text-color: var(--primary);
  --button-text-hover-color: var(--primary-dark);

  /* Legacy Variables (for backward compatibility) */
  --button-fill-hover: var(--primary-800);
  --button-outline-hover: #0056b3;
  --button-text-hover: #ffffff;

  /* Typography */
  --font-family-sans: 'Poppins', sans-serif;
  --font-family-serif: Georgia, serif;
  --font-family-mono: 'Courier New', monospace;

  /* Font Sizes - em units */
  --font-xm: 0.75em;
  --font-sm: 0.875em;
  --font-lg: 1em;
  --font-tl: 1.375em;
  --font-h6: 1.5em;
  --font-h5: 1.75em;
  --font-h4: 2em;
  --font-h3: 2.25em;
  --font-h2: 2.8125em;
  --font-h1: 3.5625em;
  --h5: 1.5em;

  /* Font Sizes - rem units (for reference) */
  --font-xs: 0.75rem;
  --font-base: 1rem;
  --font-md: 1.125rem;
  --font-xl: 1.5rem;
  --font-2xl: 1.875rem;
  --font-3xl: 2.25rem;
  --font-4xl: 3rem;
  --font-5xl: 4rem;

  /* Display sizes */
  --font-d5: 4em;
  --font-d4: 6em;
  --font-d3: 8em;
  --font-d2: 10em;
  --font-d1: 12em;

  /* Font Weights */
  --fw-thin: 100;
  --fw-extralight: 200;
  --fw-light: 300;
  --fw-regular: 400;
  --fw-medium: 500;
  --fw-semibold: 600;
  --fw-bold: 700;
  --fw-extrabold: 800;
  --fw-black: 900;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-base: 1.5;
  --line-height-loose: 1.75;
  --line-height-xm: 1.4;
  --line-height-sm: 1.4;
  --line-height-lg: 1.3;
  --line-height-tl: 1.3;
  --line-height-h6: 1.3;
  --line-height-h5: 1.2;
  --line-height-h4: 1.2;
  --line-height-h3: 1.2;
  --line-height-h2: 1.2;
  --line-height-h1: 1.2;
  --line-height-d5: 1.1;
  --line-height-d4: 1.1;
  --line-height-d3: 1.1;
  --line-height-d2: 1.1;
  --line-height-d1: 1;

  /* Spacing - em units */
  --spacing-none: 0;
  --spacing-xm: 0.125em;
  --spacing-sml: 0.25em;
  --spacing-sm: 0.5em;
  --spacing-md: 0.75em;
  --spacing-base: 1em;
  --spacing-r: 1em;
  --spacing-lg: 1.25em;
  --spacing-xl: 1.5em;
  --spacing-2md: 0.625em;
  --spacing-3md: 0.75em;
  --spacing-2r: 1.25em;
  --spacing-2lg: 1.75em;
  --spacing-2xl: 2em;
  --spacing-3xl: 3em;
  --spacing-4xl: 3.5em;
  --spacing-5xl: 4.5em;
  --spacing-6xl: 6em;
  --spacing-auto: auto;
  --spacing-half: 50%;
  --spacing-full: 100%;

  /* Border Radius - em units */
  --radius-none: 0;
  --radius-xs: 0;
  --radius-sm: 0.125em;
  --radius-base: 0.25em;
  --radius-md: 0.375em;
  --radius-lg: 0.5em;
  --radius-xl: 0.75em;
  --radius-2xl: 1em;
  --radius-3xl: 1.5em;
  --radius-4xl: 1.75em;
  --radius-5xl: 2em;
  --radius-6xl: 3.5em;
  --radius-full: 9999px;
  --radius-circle: 50%;

  /* Border Widths - px units (need to be exact) */
  --border-width-0: 0;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-3: 3px;
  --border-width-4: 4px;
  --border-width-5: 5px;

  /* Opacity */
  --opacity-0: 0;
  --opacity-10: 0.1;
  --opacity-20: 0.2;
  --opacity-30: 0.3;
  --opacity-40: 0.4;
  --opacity-50: 0.5;
  --opacity-60: 0.6;
  --opacity-70: 0.7;
  --opacity-80: 0.8;
  --opacity-90: 0.9;
  --opacity-100: 1;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Z-index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  /* Transitions */
  --transition-base: all 0.2s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;
  --transition-slower: all 0.5s ease-in-out;
  --transition-fast: all 0.1s ease-in-out;

  /* Container Widths - px units (need to be exact) */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  --breakpoint-xl: 1280px;

  /* Grid */
  --grid-cols-1: repeat(1, 1fr);
  --grid-cols-2: repeat(2, 1fr);
  --grid-cols-3: repeat(3, 1fr);
  --grid-cols-4: repeat(4, 1fr);
  --grid-cols-5: repeat(5, 1fr);
  --grid-cols-6: repeat(6, 1fr);
  --grid-cols-12: repeat(12, 1fr);

  /* Gap - px units for small values, em for larger */
  --gap-none: 0;
  --gap-xs: 2px;
  --gap-sm: 4px;
  --gap-md: 8px;
  --gap-lg: 12px;
  --gap-xl: 1em;
  --gap-2xl: 1.5em;
  --gap-3xl: 2em;
  --gap-4xl: 2.5em;
  --gap-5xl: 3em;
}
