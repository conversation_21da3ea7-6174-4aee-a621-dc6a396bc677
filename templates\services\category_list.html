{% extends 'base.html' %}
{% load static %}

{% block title %}Service Categories - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1>Service Categories</h1>
        <p>Browse our wide range of pet services</p>
    </div>

    {% if categories %}
        <div class="categories-grid">
            {% for category in categories %}
                <div class="category-card">
                    <div class="category-icon">
                        {% if category.icon %}
                            <img src="{{ category.icon.url }}" alt="{{ category.name }}" class="category-image">
                        {% elif category.icon_class %}
                            <i class="{{ category.icon_class }}"></i>
                        {% else %}
                            <i class="fas fa-paw"></i>
                        {% endif %}
                    </div>

                    <div class="category-content">
                        <h3>{{ category.name }}</h3>
                        {% if category.description %}
                            <p class="category-description">{{ category.description }}</p>
                        {% endif %}

                        <div class="category-stats">
                            <span class="provider-count">
                                <i class="fas fa-users"></i>
                                {{ category.providers.count }} providers
                            </span>
                        </div>
                    </div>

                    <div class="category-actions">
                        <a href="{% url 'category-providers' category.slug %}" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Browse Providers
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-list"></i>
            </div>
            <h3>No Categories Available</h3>
            <p>Service categories will appear here once they are added.</p>
        </div>
    {% endif %}
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--gray-200);
}

.page-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-3xl);
}

.page-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
    margin: 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.category-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-xl);
    text-align: center;
    transition: var(--transition-base);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.category-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-base);
}

.category-card:hover .category-icon {
    background: var(--primary);
    color: var(--white);
}

.category-icon i {
    font-size: 2rem;
    color: var(--primary);
    transition: var(--transition-base);
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-full);
}

.category-card:hover .category-icon i {
    color: var(--white);
}

.category-content {
    flex: 1;
    margin-bottom: var(--spacing-lg);
}

.category-content h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-xl);
}

.category-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    font-size: var(--font-sm);
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-base);
}

.provider-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-500);
    font-size: var(--font-sm);
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
}

.provider-count i {
    color: var(--primary);
}

.category-actions {
    width: 100%;
}

.category-actions .btn {
    width: 100%;
    justify-content: center;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl) var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-xl);
}

.empty-state p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-base);
    }

    .category-card {
        padding: var(--spacing-lg);
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon i {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: var(--spacing-lg) 0;
    }

    .page-header h1 {
        font-size: var(--font-2xl);
    }

    .category-card {
        padding: var(--spacing-base);
    }
}
</style>
{% endblock %}
