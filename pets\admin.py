from django.contrib import admin
from .models import (
    PetCategory, PetBreed, Pet, PetGallery, PetMedicalRecord,
    Vaccination, GrowthRecord, Milestone, FeedingSchedule, FeedingLog,
    ExerciseLog, PetExpense, PetReminder, PetDocument
)


class PetBreedInline(admin.TabularInline):
    model = PetBreed
    extra = 1


@admin.register(PetCategory)
class PetCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    inlines = [PetBreedInline]


@admin.register(PetBreed)
class PetBreedAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'average_lifespan', 'size')
    list_filter = ('category',)
    search_fields = ('name', 'description')


class PetGalleryInline(admin.TabularInline):
    model = PetGallery
    extra = 1


class PetMedicalRecordInline(admin.TabularInline):
    model = PetMedicalRecord
    extra = 0


@admin.register(Pet)
class PetAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'category', 'breed', 'gender', 'is_for_adoption')
    list_filter = ('category', 'gender', 'is_for_adoption')
    search_fields = ('name', 'owner__username', 'breed__name')
    inlines = [PetGalleryInline, PetMedicalRecordInline]


@admin.register(PetGallery)
class PetGalleryAdmin(admin.ModelAdmin):
    list_display = ('pet', 'caption', 'created_at')
    list_filter = ('pet__category',)
    search_fields = ('pet__name', 'caption')


@admin.register(PetMedicalRecord)
class PetMedicalRecordAdmin(admin.ModelAdmin):
    list_display = ('pet', 'record_date', 'record_type', 'title', 'veterinarian', 'urgency')
    list_filter = ('record_type', 'urgency', 'record_date')
    search_fields = ('pet__name', 'title', 'description', 'veterinarian', 'clinic')
    date_hierarchy = 'record_date'


@admin.register(Vaccination)
class VaccinationAdmin(admin.ModelAdmin):
    list_display = ('pet', 'vaccine_type', 'date_administered', 'next_due_date', 'veterinarian')
    list_filter = ('vaccine_type', 'date_administered')
    search_fields = ('pet__name', 'vaccine_name', 'veterinarian', 'clinic')
    date_hierarchy = 'date_administered'

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('pet')


@admin.register(GrowthRecord)
class GrowthRecordAdmin(admin.ModelAdmin):
    list_display = ('pet', 'date_recorded', 'weight', 'height', 'length')
    list_filter = ('date_recorded',)
    search_fields = ('pet__name', 'notes')
    date_hierarchy = 'date_recorded'


@admin.register(Milestone)
class MilestoneAdmin(admin.ModelAdmin):
    list_display = ('pet', 'milestone_type', 'title', 'date_achieved', 'age_at_milestone')
    list_filter = ('milestone_type', 'date_achieved')
    search_fields = ('pet__name', 'title', 'description')
    date_hierarchy = 'date_achieved'


@admin.register(FeedingSchedule)
class FeedingScheduleAdmin(admin.ModelAdmin):
    list_display = ('pet', 'meal_type', 'time', 'food_type', 'amount', 'is_active')
    list_filter = ('meal_type', 'is_active')
    search_fields = ('pet__name', 'food_type')


@admin.register(FeedingLog)
class FeedingLogAdmin(admin.ModelAdmin):
    list_display = ('pet', 'date_time', 'meal_type', 'food_type', 'amount_given')
    list_filter = ('meal_type', 'date_time')
    search_fields = ('pet__name', 'food_type', 'notes')
    date_hierarchy = 'date_time'


@admin.register(ExerciseLog)
class ExerciseLogAdmin(admin.ModelAdmin):
    list_display = ('pet', 'date', 'activity_type', 'duration_minutes', 'intensity', 'distance')
    list_filter = ('activity_type', 'intensity', 'date')
    search_fields = ('pet__name', 'location', 'notes')
    date_hierarchy = 'date'


@admin.register(PetExpense)
class PetExpenseAdmin(admin.ModelAdmin):
    list_display = ('pet', 'date', 'category', 'description', 'amount', 'vendor')
    list_filter = ('category', 'is_recurring', 'date')
    search_fields = ('pet__name', 'description', 'vendor')
    date_hierarchy = 'date'


@admin.register(PetReminder)
class PetReminderAdmin(admin.ModelAdmin):
    list_display = ('pet', 'title', 'reminder_type', 'due_date', 'is_completed', 'is_active')
    list_filter = ('reminder_type', 'frequency', 'is_completed', 'is_active')
    search_fields = ('pet__name', 'title', 'description')
    date_hierarchy = 'due_date'


@admin.register(PetDocument)
class PetDocumentAdmin(admin.ModelAdmin):
    list_display = ('pet', 'document_type', 'title', 'issue_date', 'expiry_date')
    list_filter = ('document_type', 'issue_date', 'expiry_date')
    search_fields = ('pet__name', 'title', 'description', 'issuing_authority')
