{% extends 'base.html' %}
{% load static %}

{% block title %}User Preferences - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="profile-header">
                <h1 class="page-title">User Preferences</h1>
                <p class="text-muted">Manage your privacy, notifications, and activity settings</p>
            </div>

            <form method="post" class="preferences-form">
                {% csrf_token %}
                
                <!-- Privacy Settings -->
                <div class="preferences-section">
                    <h3 class="section-title">
                        <i class="fas fa-shield-alt"></i>
                        Privacy Settings
                    </h3>
                    
                    <div class="form-group">
                        <label for="privacy_level">Profile Visibility</label>
                        <select name="privacy_level" id="privacy_level" class="form-control">
                            <option value="public" {% if user.privacy_level == 'public' %}selected{% endif %}>
                                Public - Anyone can view your profile
                            </option>
                            <option value="friends" {% if user.privacy_level == 'friends' %}selected{% endif %}>
                                Friends Only - Only people you follow can view your profile
                            </option>
                            <option value="private" {% if user.privacy_level == 'private' %}selected{% endif %}>
                                Private - Only you can view your profile
                            </option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_activity_feed" id="show_activity_feed" 
                               class="form-check-input" {% if preferences.show_activity_feed %}checked{% endif %}>
                        <label for="show_activity_feed" class="form-check-label">
                            Show my activity feed to others
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_wishlist" id="show_wishlist" 
                               class="form-check-input" {% if preferences.show_wishlist %}checked{% endif %}>
                        <label for="show_wishlist" class="form-check-label">
                            Show my wishlist to others
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_pets" id="show_pets" 
                               class="form-check-input" {% if preferences.show_pets %}checked{% endif %}>
                        <label for="show_pets" class="form-check-label">
                            Show my pets to others
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_reviews" id="show_reviews" 
                               class="form-check-input" {% if preferences.show_reviews %}checked{% endif %}>
                        <label for="show_reviews" class="form-check-label">
                            Show my reviews to others
                        </label>
                    </div>
                </div>

                <!-- Activity Feed Preferences -->
                <div class="preferences-section">
                    <h3 class="section-title">
                        <i class="fas fa-stream"></i>
                        Activity Feed Preferences
                    </h3>
                    <p class="section-description">Choose which activities to include in your feed</p>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_pet_activities" id="show_pet_activities" 
                               class="form-check-input" {% if preferences.show_pet_activities %}checked{% endif %}>
                        <label for="show_pet_activities" class="form-check-label">
                            Pet-related activities (adding pets, adoptions, etc.)
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_service_activities" id="show_service_activities" 
                               class="form-check-input" {% if preferences.show_service_activities %}checked{% endif %}>
                        <label for="show_service_activities" class="form-check-label">
                            Service activities (bookings, reviews, etc.)
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_shop_activities" id="show_shop_activities" 
                               class="form-check-input" {% if preferences.show_shop_activities %}checked{% endif %}>
                        <label for="show_shop_activities" class="form-check-label">
                            Shopping activities (orders, reviews, etc.)
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="show_social_activities" id="show_social_activities" 
                               class="form-check-input" {% if preferences.show_social_activities %}checked{% endif %}>
                        <label for="show_social_activities" class="form-check-label">
                            Social activities (following users/pets, etc.)
                        </label>
                    </div>
                </div>

                <!-- Notification Preferences -->
                <div class="preferences-section">
                    <h3 class="section-title">
                        <i class="fas fa-bell"></i>
                        Notification Preferences
                    </h3>
                    
                    <div class="form-check">
                        <input type="checkbox" name="email_notifications" id="email_notifications" 
                               class="form-check-input" {% if user.email_notifications %}checked{% endif %}>
                        <label for="email_notifications" class="form-check-label">
                            Enable email notifications
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="push_notifications" id="push_notifications" 
                               class="form-check-input" {% if user.push_notifications %}checked{% endif %}>
                        <label for="push_notifications" class="form-check-label">
                            Enable push notifications
                        </label>
                    </div>
                    
                    <hr class="notification-divider">
                    
                    <div class="form-check">
                        <input type="checkbox" name="notify_on_pet_inquiry" id="notify_on_pet_inquiry" 
                               class="form-check-input" {% if preferences.notify_on_pet_inquiry %}checked{% endif %}>
                        <label for="notify_on_pet_inquiry" class="form-check-label">
                            Pet adoption inquiries
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="notify_on_service_booking" id="notify_on_service_booking" 
                               class="form-check-input" {% if preferences.notify_on_service_booking %}checked{% endif %}>
                        <label for="notify_on_service_booking" class="form-check-label">
                            Service bookings and updates
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="notify_on_order_update" id="notify_on_order_update" 
                               class="form-check-input" {% if preferences.notify_on_order_update %}checked{% endif %}>
                        <label for="notify_on_order_update" class="form-check-label">
                            Order status updates
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="notify_on_new_follower" id="notify_on_new_follower" 
                               class="form-check-input" {% if preferences.notify_on_new_follower %}checked{% endif %}>
                        <label for="notify_on_new_follower" class="form-check-label">
                            New followers
                        </label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="notify_on_review" id="notify_on_review" 
                               class="form-check-input" {% if preferences.notify_on_review %}checked{% endif %}>
                        <label for="notify_on_review" class="form-check-label">
                            Reviews and ratings
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Preferences
                    </button>
                    <a href="{% url 'user-profile' user.username %}" class="btn btn-outline-secondary">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.preferences-form {
    margin-top: 2rem;
}

.preferences-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-title {
    color: var(--text-primary);
    font-size: 1.25rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-check {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.form-check-input {
    margin-left: -1.5rem;
}

.form-check-label {
    color: var(--text-primary);
    font-weight: 400;
}

.notification-divider {
    margin: 1.5rem 0;
    border-color: var(--border-color);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .preferences-section {
        padding: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        justify-content: center;
    }
}
</style>
{% endblock %}
