{% extends 'base.html' %}

{% block title %}{{ profile_user.full_name|default:profile_user.email }}'s Profile | PetPaw{% endblock %}

{% block extra_css %}
<style>
    /* Modern Profile Header */
    .profile-header {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 80px;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        z-index: 0;
    }

    .profile-header-content {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .profile-picture-wrapper {
        position: relative;
    }

    .profile-picture {
        width: 150px;
        height: 150px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .profile-info {
        flex: 1;
    }

    .profile-name-main {
        font-size: var(--font-3xl);
        font-weight: var(--fw-bold);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text);
        line-height: 1.2;
    }

    .verified-badge {
        color: var(--accent2);
        font-size: 1.2rem;
    }

    .profile-name {
        color: var(--text-light);
        margin-bottom: var(--spacing-base);
    }

    .profile-bio {
        margin-bottom: var(--spacing-lg);
        line-height: 1.6;
    }

    .profile-stats {
        display: flex;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-lg);
    }

    .stat-item {
        text-align: center;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .stat-item:hover {
        transform: translateY(-2px);
    }

    .stat-value {
        font-size: var(--font-xl);
        font-weight: var(--fw-bold);
    }

    .stat-label {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    .profile-actions {
        display: flex;
        gap: var(--spacing-base);
    }

    .btn-follow {
        background-color: var(--primary);
        color: var(--white);
        border: none;
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-follow:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-unfollow {
        background-color: transparent;
        color: var(--text);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-unfollow:hover {
        background-color: var(--gray-100);
        box-shadow: var(--shadow-sm);
    }

    .btn-message {
        background-color: transparent;
        color: var(--text);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-message:hover {
        background-color: var(--gray-100);
        box-shadow: var(--shadow-sm);
    }

    /* Tabs Styling */
    .profile-tabs {
        margin-bottom: var(--spacing-xl);
    }

    .tab-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--gray-200);
    }

    .tab-item {
        margin-right: var(--spacing-xl);
    }

    .tab-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: var(--spacing-base) 0;
        color: var(--text-light);
        font-weight: var(--fw-medium);
        position: relative;
        transition: all 0.3s ease;
    }

    .tab-link i {
        font-size: 1.2rem;
    }

    .tab-link.active {
        color: var(--primary);
    }

    .tab-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary);
    }

    /* Tab Content */
    .tab-content {
        position: relative;
        min-height: 200px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Grid Layouts */
    .pet-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--gap-xl);
    }

    .post-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--gap-lg);
    }

    /* Card Styling */
    .card {
        background-color: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }

    .card-img-top {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .card-body {
        padding: 1.25rem;
    }

    .card-title {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .card-text {
        color: var(--text-light);
        margin-bottom: 1rem;
    }



    /* Empty State */
    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl) var(--spacing-base);
        background-color: var(--gray-50);
        border-radius: var(--radius-xl);
        border: 1px dashed var(--gray-300);
    }

    .empty-state p {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
        font-size: var(--font-md);
    }

    /* Role Badges */
    .profile-roles {
        margin-bottom: var(--spacing-base);
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }

    .role-badge {
        background-color: var(--gray-100);
        color: var(--text);
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
        font-weight: var(--fw-medium);
    }

    .role-badge.primary-role {
        background-color: var(--primary);
        color: var(--white);
    }

    /* Activity Section */
    .activity-section, .wishlist-section {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-sm);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        padding-bottom: var(--spacing-base);
    }

    .view-all-link {
        color: var(--primary);
        font-weight: var(--fw-medium);
        text-decoration: none;
        font-size: var(--font-sm);
    }

    .view-all-link:hover {
        text-decoration: underline;
    }

    .activity-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-base);
    }

    .activity-item-mini {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-base);
        padding: var(--spacing-base);
        border-radius: var(--radius-base);
        transition: background-color 0.2s;
    }

    .activity-item-mini:hover {
        background-color: var(--gray-50);
    }

    .activity-icon-mini {
        font-size: 1.2rem;
        width: 1.5rem;
        text-align: center;
    }

    .activity-content-mini {
        flex: 1;
    }

    .activity-description {
        margin: 0 0 0.25rem 0;
        font-weight: var(--fw-medium);
    }

    .activity-time {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    /* Wishlist Section */
    .wishlist-subsection {
        margin-bottom: var(--spacing-xl);
    }

    .wishlist-subsection h4 {
        margin-bottom: var(--spacing-base);
        color: var(--text);
        font-size: var(--font-lg);
    }

    .wishlist-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-base);
    }

    .wishlist-card-mini {
        display: flex;
        align-items: center;
        gap: var(--spacing-base);
        padding: var(--spacing-base);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-base);
        transition: all 0.2s;
    }

    .wishlist-card-mini:hover {
        box-shadow: var(--shadow-sm);
        transform: translateY(-2px);
    }

    .wishlist-thumbnail {
        width: 3rem;
        height: 3rem;
        border-radius: var(--radius-base);
        object-fit: cover;
    }

    .wishlist-info h5 {
        margin: 0 0 0.25rem 0;
        font-size: var(--font-base);
        font-weight: var(--fw-medium);
    }

    .wishlist-info p {
        margin: 0;
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .profile-header-content {
            flex-direction: column;
            text-align: center;
        }

        .profile-name-main {
            font-size: var(--font-2xl);
            text-align: center;
        }

        .profile-stats {
            justify-content: center;
        }

        .profile-actions {
            justify-content: center;
            flex-wrap: wrap;
        }

        .post-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        .wishlist-grid {
            grid-template-columns: 1fr;
        }

        .profile-roles {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="profile-header">
        <div class="profile-header-content">
            <div class="profile-picture-wrapper">
                <img src="{{ profile_user.profile_picture.url }}" alt="{{ profile_user.full_name|default:profile_user.email }}" class="profile-picture">
            </div>

            <div class="profile-info">
                <h1 class="profile-name-main">
                    {% if profile_user.first_name and profile_user.last_name %}
                        {{ profile_user.first_name }} {{ profile_user.last_name }}
                    {% elif profile_user.full_name %}
                        {{ profile_user.full_name }}
                    {% else %}
                        {{ profile_user.email }}
                    {% endif %}
                    {% if profile_user.is_service_provider %}
                        <span class="verified-badge" title="Verified Service Provider"><i class="fas fa-check-circle"></i></span>
                    {% endif %}
                </h1>

                {% if user_roles %}
                    <div class="profile-roles">
                        {% for role in user_roles %}
                            <span class="role-badge {% if role == primary_role %}primary-role{% endif %}">
                                {{ role.display_name }}
                            </span>
                        {% endfor %}
                    </div>
                {% endif %}

                <p class="profile-bio">{{ profile_user.bio|default:"No bio yet." }}</p>

                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_pets_count }}</div>
                        <div class="stat-label">Pets</div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_followers_count }}</div>
                        <div class="stat-label">Followers</div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_following_count }}</div>
                        <div class="stat-label">Following</div>
                    </div>
                </div>

                <div class="profile-actions">
                    {% if user.is_authenticated and user != profile_user %}
                        {% if is_following %}
                            <a href="{% url 'follow-user' user_id=profile_user.id %}" class="btn-unfollow">
                                <i class="fas fa-user-minus"></i> Unfollow
                            </a>
                        {% else %}
                            <a href="{% url 'follow-user' user_id=profile_user.id %}" class="btn-follow">
                                <i class="fas fa-user-plus"></i> Follow
                            </a>
                        {% endif %}

                        <a href="{% url 'start-conversation' user_id=profile_user.id %}" class="btn-message">
                            <i class="fas fa-envelope"></i> Message
                        </a>
                    {% elif user == profile_user %}
                        <a href="/users/profile/edit/" class="btn-message">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                        <a href="{% url 'user-preferences' %}" class="btn-message">
                            <i class="fas fa-cog"></i> Preferences
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="profile-tabs">
        <ul class="tab-list">
            <li class="tab-item">
                <a href="#pets" class="tab-link active">
                    <i class="fas fa-paw"></i> Pets
                </a>
            </li>

            {% if activities %}
                <li class="tab-item">
                    <a href="#activity" class="tab-link">
                        <i class="fas fa-stream"></i> Activity
                    </a>
                </li>
            {% endif %}

            {% if wishlist_pets or wishlist_products or user == profile_user %}
                <li class="tab-item">
                    <a href="#wishlist" class="tab-link">
                        <i class="fas fa-heart"></i> Wishlist
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>

    <div class="tab-content">
        <!-- Pets Tab -->
        <div id="pets" class="tab-pane active">
            {% if pets %}
                <div class="pet-grid">
                    {% for pet in pets %}
                        <div class="card">
                            <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="card-img-top">
                            <div class="card-body">
                                <h3 class="card-title">{{ pet.name }}</h3>
                                <p class="card-text">{{ pet.breed.name }}</p>
                                <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-primary">View Profile</a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <p>No pets added yet.</p>
                    {% if user == profile_user %}
                        <a href="{% url 'pet-create' %}" class="btn btn-primary">Add a Pet</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Activity Tab -->
        {% if activities %}
            <div id="activity" class="tab-pane">
                <div class="activity-section">
                    <div class="section-header">
                        <h3>Recent Activity</h3>
                        {% if user == profile_user %}
                            <a href="{% url 'user-activity-feed' %}" class="view-all-link">View All</a>
                        {% endif %}
                    </div>

                    <div class="activity-list">
                        {% for activity in activities %}
                            <div class="activity-item-mini">
                                <div class="activity-icon-mini">
                                    {% if activity.activity_type == 'pet_added' %}
                                        <i class="fas fa-plus-circle text-success"></i>
                                    {% elif activity.activity_type == 'review_written' %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% elif activity.activity_type == 'service_booked' %}
                                        <i class="fas fa-calendar-check text-primary"></i>
                                    {% else %}
                                        <i class="fas fa-circle text-secondary"></i>
                                    {% endif %}
                                </div>
                                <div class="activity-content-mini">
                                    <p class="activity-description">{{ activity.description }}</p>
                                    <small class="activity-time">{{ activity.created_at|timesince }} ago</small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Wishlist Tab -->
        {% if wishlist_pets or wishlist_products or user == profile_user %}
            <div id="wishlist" class="tab-pane">
                <div class="wishlist-section">
                    <div class="section-header">
                        <h3>Wishlist</h3>
                        {% if user == profile_user %}
                            <a href="{% url 'user-wishlist' %}" class="view-all-link">View All</a>
                        {% endif %}
                    </div>

                    {% if wishlist_pets %}
                        <div class="wishlist-subsection">
                            <h4>Saved Pets</h4>
                            <div class="wishlist-grid">
                                {% for item in wishlist_pets %}
                                    <div class="wishlist-card-mini">
                                        <img src="{{ item.content_object.profile_picture.url }}"
                                             alt="{{ item.content_object.name }}"
                                             class="wishlist-thumbnail">
                                        <div class="wishlist-info">
                                            <h5>{{ item.content_object.name }}</h5>
                                            <p>{{ item.content_object.category.name }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    {% if wishlist_products %}
                        <div class="wishlist-subsection">
                            <h4>Saved Products</h4>
                            <div class="wishlist-grid">
                                {% for item in wishlist_products %}
                                    <div class="wishlist-card-mini">
                                        <img src="{{ item.content_object.image.url }}"
                                             alt="{{ item.content_object.name }}"
                                             class="wishlist-thumbnail">
                                        <div class="wishlist-info">
                                            <h5>{{ item.content_object.name }}</h5>
                                            <p>${{ item.content_object.get_current_price }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    {% if not wishlist_pets and not wishlist_products %}
                        <div class="empty-state">
                            <p>No saved items yet.</p>
                            {% if user == profile_user %}
                                <a href="{% url 'pet-list' %}" class="btn btn-primary">Browse Pets</a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabPanes = document.querySelectorAll('.tab-pane');

        // Function to activate a tab
        function activateTab(tabId) {
            // Remove active class from all tabs
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to selected tab
            const selectedTab = document.querySelector(`.tab-link[href="#${tabId}"]`);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Show corresponding tab content
            const selectedPane = document.getElementById(tabId);
            if (selectedPane) {
                selectedPane.classList.add('active');
            }

            // Update URL hash
            window.location.hash = tabId;
        }

        // Handle tab clicks
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = this.getAttribute('href').substring(1);
                activateTab(target);
            });
        });

        // Check for hash in URL on page load
        if (window.location.hash) {
            const tabId = window.location.hash.substring(1);
            const validTabs = Array.from(tabPanes).map(pane => pane.id);

            if (validTabs.includes(tabId)) {
                activateTab(tabId);
            }
        }

        // Add hover effects to post cards
        const postCards = document.querySelectorAll('.post-card');
        postCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.querySelector('.post-overlay').style.opacity = '1';
            });

            card.addEventListener('mouseleave', function() {
                this.querySelector('.post-overlay').style.opacity = '0';
            });
        });

        // Add animation to stats on hover
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
