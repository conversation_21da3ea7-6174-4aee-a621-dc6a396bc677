{% extends 'base.html' %}
{% load static %}

{% block title %}{{ provider.user.get_full_name|default:provider.user.username }} - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="provider-detail-container">
        <!-- Provider Header -->
        <div class="provider-header">
            <div class="provider-avatar">
                {% if provider.profile_picture %}
                    <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.username }}">
                {% else %}
                    <div class="avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
            </div>

            <div class="provider-info">
                <h1>{{ provider.user.full_name|default:provider.user.email }}</h1>
                <div class="provider-rating">
                    <div class="stars">
                        {% for i in "12345" %}
                            {% if forloop.counter <= provider.rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="rating-text">{{ provider.rating|floatformat:1 }} ({{ reviews.count }} reviews)</span>
                </div>

                <div class="provider-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ provider.experience_years }} years experience</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>${{ provider.hourly_rate }}/hour</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ provider.location|default:"Location not specified" }}</span>
                    </div>
                </div>

                <div class="availability-status">
                    {% if provider.is_available %}
                        <span class="status-badge available">
                            <i class="fas fa-check-circle"></i>
                            Available
                        </span>
                    {% else %}
                        <span class="status-badge unavailable">
                            <i class="fas fa-times-circle"></i>
                            Unavailable
                        </span>
                    {% endif %}
                </div>

                <!-- General booking button for multiple services -->
                {% if provider.is_available and services.count > 1 %}
                    <div class="provider-actions">
                        <a href="{% url 'book-provider' provider.pk %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar-plus"></i>
                            Book Service
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Provider Bio -->
        {% if provider.bio %}
            <div class="provider-bio">
                <h2>About</h2>
                <p>{{ provider.bio }}</p>
            </div>
        {% endif %}

        <!-- Services -->
        <div class="provider-services">
            <h2>Services</h2>
            {% if services %}
                <div class="services-grid">
                    {% for service in services %}
                        <div class="service-card">
                            <div class="service-header">
                                <h3>{{ service.name }}</h3>
                                <span class="service-price">${{ service.price_per_hour }}/hour</span>
                            </div>
                            <p class="service-description">{{ service.description|truncatewords:20 }}</p>
                            <div class="service-duration">
                                <i class="fas fa-clock"></i>
                                <span>{{ service.duration }} hours</span>
                            </div>
                            <div class="service-actions">
                                <a href="{% url 'book-service' service.pk %}" class="btn btn-primary">
                                    <i class="fas fa-calendar-plus"></i>
                                    Book Service
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <p>No services available at the moment.</p>
                </div>
            {% endif %}
        </div>

        <!-- Availability -->
        {% if availability %}
            <div class="provider-availability">
                <h2>Availability</h2>
                <div class="availability-grid">
                    {% regroup availability by day_of_week as day_list %}
                    {% for day in day_list %}
                        <div class="availability-day">
                            <h4>{{ day.grouper|date:"l" }}</h4>
                            <div class="time-slots">
                                {% for slot in day.list %}
                                    <span class="time-slot">
                                        {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                    </span>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Gallery -->
        {% if gallery %}
            <div class="provider-gallery">
                <h2>Gallery</h2>
                <div class="gallery-grid">
                    {% for image in gallery %}
                        <div class="gallery-item">
                            <img src="{{ image.image.url }}" alt="{{ image.caption|default:'Gallery image' }}">
                            {% if image.caption %}
                                <div class="gallery-caption">{{ image.caption }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Reviews -->
        {% if reviews %}
            <div class="provider-reviews">
                <h2>Reviews</h2>
                <div class="reviews-list">
                    {% for review in reviews %}
                        <div class="review-card">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <strong>{{ review.booking.user.get_full_name|default:review.booking.user.username }}</strong>
                                    <div class="review-rating">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <span class="review-date">{{ review.created_at|date:"M d, Y" }}</span>
                            </div>
                            <p class="review-comment">{{ review.comment }}</p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
.provider-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.provider-header {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.provider-avatar {
    width: 120px;
    height: 120px;
    border-radius: var(--radius-full);
    overflow: hidden;
    background: var(--gray-200);
    flex-shrink: 0;
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: 3rem;
}

.provider-info {
    flex: 1;
}

.provider-info h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-2xl);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-base);
}

.stars {
    color: var(--warning);
    font-size: var(--font-lg);
}

.rating-text {
    color: var(--gray-600);
    font-size: var(--font-sm);
}

.provider-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-700);
    font-size: var(--font-sm);
}

.meta-item i {
    color: var(--primary);
    width: 16px;
}

.availability-status {
    margin-top: var(--spacing-base);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
    font-weight: 600;
}

.status-badge.available {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-badge.unavailable {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.provider-actions {
    margin-top: var(--spacing-lg);
}

.provider-actions .btn {
    width: 100%;
    justify-content: center;
}

.provider-bio,
.provider-services,
.provider-availability,
.provider-gallery,
.provider-reviews {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.provider-bio h2,
.provider-services h2,
.provider-availability h2,
.provider-gallery h2,
.provider-reviews h2 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-xl);
}

.provider-bio p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.service-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    transition: var(--transition-base);
}

.service-card:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.service-header h3 {
    color: var(--primary);
    margin: 0;
    font-size: var(--font-lg);
}

.service-price {
    color: var(--primary);
    font-weight: 600;
    font-size: var(--font-lg);
}

.service-description {
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.service-duration {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-600);
    font-size: var(--font-sm);
    margin-bottom: var(--spacing-base);
}

.service-duration i {
    color: var(--primary);
}

.service-actions {
    text-align: right;
}

.availability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-base);
}

.availability-day h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-base);
}

.time-slots {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-slot {
    background: var(--primary-light);
    color: var(--primary-dark);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-sm);
    text-align: center;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-base);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
}

.reviews-list {
    display: grid;
    gap: var(--spacing-base);
}

.review-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-base);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.reviewer-info strong {
    color: var(--gray-800);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.review-rating {
    color: var(--warning);
    font-size: var(--font-sm);
}

.review-date {
    color: var(--gray-500);
    font-size: var(--font-sm);
}

.review-comment {
    color: var(--gray-700);
    line-height: 1.5;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--gray-500);
}

@media (max-width: 768px) {
    .provider-header {
        flex-direction: column;
        text-align: center;
    }

    .provider-meta {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .availability-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}
