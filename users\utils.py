from django.contrib.contenttypes.models import ContentType
from .models import UserActivity, Wishlist


def create_user_activity(user, activity_type, description, content_object=None, is_public=True):
    """
    Create a user activity record
    
    Args:
        user: User instance
        activity_type: Type of activity (from UserActivity.ACTIVITY_TYPES)
        description: Description of the activity
        content_object: Optional related object (Pet, Product, Service, etc.)
        is_public: Whether the activity should be visible to others
    """
    activity_data = {
        'user': user,
        'activity_type': activity_type,
        'description': description,
        'is_public': is_public,
    }
    
    if content_object:
        activity_data['content_type'] = ContentType.objects.get_for_model(content_object)
        activity_data['object_id'] = content_object.pk
    
    return UserActivity.objects.create(**activity_data)


def add_to_wishlist(user, item, item_type, notes=''):
    """
    Add an item to user's wishlist
    
    Args:
        user: User instance
        item: The object to add (Pet, Product, Service)
        item_type: Type of item ('pet', 'product', 'service')
        notes: Optional notes about the item
    
    Returns:
        Wishlist instance or None if already exists
    """
    content_type = ContentType.objects.get_for_model(item)
    
    wishlist_item, created = Wishlist.objects.get_or_create(
        user=user,
        content_type=content_type,
        object_id=item.pk,
        defaults={
            'item_type': item_type,
            'notes': notes,
        }
    )
    
    if created:
        # Create activity for adding to wishlist
        create_user_activity(
            user=user,
            activity_type='pet_followed' if item_type == 'pet' else 'product_saved',
            description=f"Added {item} to wishlist",
            content_object=item,
            is_public=False
        )
    
    return wishlist_item if created else None


def remove_from_wishlist(user, item):
    """
    Remove an item from user's wishlist
    
    Args:
        user: User instance
        item: The object to remove
    
    Returns:
        Boolean indicating if item was removed
    """
    content_type = ContentType.objects.get_for_model(item)
    
    try:
        wishlist_item = Wishlist.objects.get(
            user=user,
            content_type=content_type,
            object_id=item.pk
        )
        wishlist_item.delete()
        return True
    except Wishlist.DoesNotExist:
        return False


def is_in_wishlist(user, item):
    """
    Check if an item is in user's wishlist
    
    Args:
        user: User instance
        item: The object to check
    
    Returns:
        Boolean indicating if item is in wishlist
    """
    if not user.is_authenticated:
        return False
        
    content_type = ContentType.objects.get_for_model(item)
    
    return Wishlist.objects.filter(
        user=user,
        content_type=content_type,
        object_id=item.pk
    ).exists()


def get_user_activity_feed(user, limit=20, activity_types=None):
    """
    Get user's activity feed
    
    Args:
        user: User instance
        limit: Number of activities to return
        activity_types: List of activity types to filter by
    
    Returns:
        QuerySet of UserActivity instances
    """
    activities = UserActivity.objects.filter(user=user, is_public=True)
    
    if activity_types:
        activities = activities.filter(activity_type__in=activity_types)
    
    return activities[:limit]


def get_user_wishlist(user, item_type=None):
    """
    Get user's wishlist items
    
    Args:
        user: User instance
        item_type: Optional filter by item type ('pet', 'product', 'service')
    
    Returns:
        QuerySet of Wishlist instances
    """
    wishlist = Wishlist.objects.filter(user=user)
    
    if item_type:
        wishlist = wishlist.filter(item_type=item_type)
    
    return wishlist


def assign_user_role(user, role_name, is_primary=False):
    """
    Assign a role to a user
    
    Args:
        user: User instance
        role_name: Name of the role to assign
        is_primary: Whether this should be the user's primary role
    
    Returns:
        Boolean indicating success
    """
    from .models import UserRole
    
    try:
        role = UserRole.objects.get(name=role_name)
        user.roles.add(role)
        
        if is_primary:
            user.primary_role = role
            user.save()
        
        # Create activity for role assignment
        create_user_activity(
            user=user,
            activity_type='became_provider' if role_name == 'service_provider' else 'profile_updated',
            description=f"Became a {role.display_name}",
            is_public=True
        )
        
        return True
    except UserRole.DoesNotExist:
        return False


def check_user_permission(user, permission):
    """
    Check if user has a specific permission
    
    Args:
        user: User instance
        permission: Permission name (e.g., 'can_add_pets')
    
    Returns:
        Boolean indicating if user has permission
    """
    if not user.is_authenticated:
        return False
    
    return user.has_permission(permission)
