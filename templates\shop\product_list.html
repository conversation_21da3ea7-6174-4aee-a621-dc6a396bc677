{% extends "base.html" %}
{% load static %}

{% block title %}Pet Products | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-xl);
        margin-top: var(--spacing-xl);
    }

    .product-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .product-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .product-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .wishlist-btn-card {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        z-index: 2;
        backdrop-filter: blur(10px);
    }

    .wishlist-btn-card:hover {
        background: var(--white);
        transform: scale(1.1);
    }

    .wishlist-btn-card.in-wishlist {
        background: var(--primary-color);
        color: white;
    }

    .product-content {
        padding: var(--spacing-lg);
    }

    .product-category {
        color: var(--text-secondary);
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-xs);
    }

    .product-title {
        font-size: var(--font-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
    }

    .product-title a {
        color: inherit;
        text-decoration: none;
    }

    .product-title a:hover {
        color: var(--primary-color);
    }

    .product-price {
        font-size: var(--font-xl);
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: var(--spacing-base);
    }

    .product-actions {
        display: flex;
        gap: var(--spacing-sm);
    }

    .btn-add-cart {
        flex: 1;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: var(--border-radius);
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn-add-cart:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
    }

    .filters-section {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-base);
        margin-bottom: var(--spacing-base);
    }

    .search-bar {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: var(--font-base);
    }

    .search-bar:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
    }

    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl) var(--spacing-xl);
        color: var(--text-secondary);
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1 class="page-title">Pet Products</h1>
        <p class="text-muted">Everything your pet needs, all in one place</p>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filters-grid">
                <div class="form-group">
                    <label for="search">Search Products</label>
                    <input type="text" name="search" id="search" class="search-bar"
                           placeholder="Search for products..." value="{{ search_query }}">
                </div>

                <div class="form-group">
                    <label for="category">Category</label>
                    <select name="category" id="category" class="form-control">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}"
                                    {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="pet_category">Pet Type</label>
                    <select name="pet_category" id="pet_category" class="form-control">
                        <option value="">All Pet Types</option>
                        {% for pet_category in pet_categories %}
                            <option value="{{ pet_category.id }}"
                                    {% if current_pet_category == pet_category.id|stringformat:"s" %}selected{% endif %}>
                                {{ pet_category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="sort_by">Sort By</label>
                    <select name="sort_by" id="sort_by" class="form-control">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name A-Z</option>
                        <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                        <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Newest First</option>
                    </select>
                </div>
            </div>

            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="{% url 'product-list' %}" class="btn btn-outline-secondary">Clear All</a>
            </div>
        </form>
    </div>

    <!-- Products Grid -->
    {% if products %}
        <div class="product-grid">
            {% for product in products %}
                <div class="product-card">
                    <div class="product-image">
                        <img src="{{ product.image.url }}" alt="{{ product.name }}">

                        {% if user.is_authenticated %}
                            <button class="wishlist-btn-card" data-item-id="{{ product.id }}" data-item-type="product"
                                    title="Add to wishlist">
                                <i class="far fa-heart"></i>
                            </button>
                        {% endif %}
                    </div>

                    <div class="product-content">
                        <div class="product-category">{{ product.category.name }}</div>
                        <h3 class="product-title">
                            <a href="{% url 'product-detail' pk=product.pk %}">{{ product.name }}</a>
                        </h3>
                        <div class="product-price">${{ product.get_current_price }}</div>

                        <div class="product-actions">
                            <button class="btn-add-cart" data-product-id="{{ product.id }}">
                                <i class="fas fa-shopping-cart"></i> Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <nav aria-label="Product pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo; First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Last &raquo;</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <h3>No Products Found</h3>
            <p>We couldn't find any products matching your criteria. Try adjusting your filters.</p>
            <a href="{% url 'product-list' %}" class="btn btn-primary">View All Products</a>
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Wishlist functionality
    const wishlistButtons = document.querySelectorAll('.wishlist-btn-card');

    wishlistButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const itemId = this.dataset.itemId;
            const itemType = this.dataset.itemType;
            const icon = this.querySelector('i');

            // Disable button during request
            this.disabled = true;

            fetch('{% url "toggle-wishlist" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: `item_id=${itemId}&item_type=${itemType}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Toggle wishlist state
                    if (data.in_wishlist) {
                        this.classList.add('in-wishlist');
                        icon.className = 'fas fa-heart';
                        this.title = 'Remove from wishlist';

                        // Add animation
                        this.style.transform = 'scale(1.2)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 200);
                    } else {
                        this.classList.remove('in-wishlist');
                        icon.className = 'far fa-heart';
                        this.title = 'Add to wishlist';
                    }
                } else {
                    alert('Error: ' + data.error);
                }
                this.disabled = false;
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating wishlist.');
                this.disabled = false;
            });
        });
    });

    // Add to cart functionality
    const addToCartButtons = document.querySelectorAll('.btn-add-cart');

    addToCartButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.dataset.productId;

            // Disable button during request
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

            fetch(`/shop/add-to-cart/${productId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.innerHTML = '<i class="fas fa-check"></i> Added!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-shopping-cart"></i> Add to Cart';
                        this.disabled = false;
                    }, 2000);
                } else {
                    alert('Error adding to cart');
                    this.innerHTML = '<i class="fas fa-shopping-cart"></i> Add to Cart';
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while adding to cart.');
                this.innerHTML = '<i class="fas fa-shopping-cart"></i> Add to Cart';
                this.disabled = false;
            });
        });
    });

    // CSRF token helper function
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
</script>

{% csrf_token %}
{% endblock %}