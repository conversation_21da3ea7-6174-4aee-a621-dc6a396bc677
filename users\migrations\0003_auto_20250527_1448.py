# Generated by Django 4.2.7 on 2025-05-27 09:18

from django.db import migrations


def create_default_roles(apps, schema_editor):
    """Create default user roles"""
    UserRole = apps.get_model('users', 'UserRole')

    roles_data = [
        {
            'name': 'regular',
            'display_name': 'Regular User',
            'description': 'Standard user with basic permissions',
            'can_add_pets': True,
            'can_sell_pets': False,
            'can_provide_services': False,
            'can_sell_products': False,
            'can_moderate_content': False,
            'can_manage_users': False,
            'can_access_analytics': False,
        },
        {
            'name': 'pet_owner',
            'display_name': 'Pet Owner',
            'description': 'User who owns pets and can list them for adoption',
            'can_add_pets': True,
            'can_sell_pets': True,
            'can_provide_services': False,
            'can_sell_products': False,
            'can_moderate_content': False,
            'can_manage_users': False,
            'can_access_analytics': False,
        },
        {
            'name': 'service_provider',
            'display_name': 'Service Provider',
            'description': 'User who provides pet-related services',
            'can_add_pets': True,
            'can_sell_pets': False,
            'can_provide_services': True,
            'can_sell_products': False,
            'can_moderate_content': False,
            'can_manage_users': False,
            'can_access_analytics': False,
        },
        {
            'name': 'shop_vendor',
            'display_name': 'Shop Vendor',
            'description': 'User who sells pet products',
            'can_add_pets': True,
            'can_sell_pets': False,
            'can_provide_services': False,
            'can_sell_products': True,
            'can_moderate_content': False,
            'can_manage_users': False,
            'can_access_analytics': False,
        },
        {
            'name': 'moderator',
            'display_name': 'Moderator',
            'description': 'User who can moderate content and help manage the community',
            'can_add_pets': True,
            'can_sell_pets': True,
            'can_provide_services': True,
            'can_sell_products': True,
            'can_moderate_content': True,
            'can_manage_users': False,
            'can_access_analytics': True,
        },
        {
            'name': 'admin',
            'display_name': 'Administrator',
            'description': 'User with full administrative privileges',
            'can_add_pets': True,
            'can_sell_pets': True,
            'can_provide_services': True,
            'can_sell_products': True,
            'can_moderate_content': True,
            'can_manage_users': True,
            'can_access_analytics': True,
        },
    ]

    for role_data in roles_data:
        UserRole.objects.get_or_create(
            name=role_data['name'],
            defaults=role_data
        )


def reverse_create_default_roles(apps, schema_editor):
    """Remove default user roles"""
    UserRole = apps.get_model('users', 'UserRole')
    UserRole.objects.filter(
        name__in=['regular', 'pet_owner', 'service_provider', 'shop_vendor', 'moderator', 'admin']
    ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_userrole_user_email_notifications_user_privacy_level_and_more'),
    ]

    operations = [
        migrations.RunPython(create_default_roles, reverse_create_default_roles),
    ]
