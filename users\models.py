from django.db import models
from django.contrib.auth.models import AbstractUser
from django.urls import reverse
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class UserRole(models.Model):
    """Model for user roles with granular permissions"""
    ROLE_CHOICES = (
        ('regular', 'Regular User'),
        ('pet_owner', 'Pet Owner'),
        ('service_provider', 'Service Provider'),
        ('shop_vendor', 'Shop Vendor'),
        ('moderator', 'Moderator'),
        ('admin', 'Administrator'),
    )

    name = models.CharField(max_length=20, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=50)
    description = models.TextField(blank=True)

    # Permissions
    can_add_pets = models.BooleanField(default=False)
    can_sell_pets = models.BooleanField(default=False)
    can_provide_services = models.BooleanField(default=False)
    can_sell_products = models.BooleanField(default=False)
    can_moderate_content = models.BooleanField(default=False)
    can_manage_users = models.BooleanField(default=False)
    can_access_analytics = models.BooleanField(default=False)

    def __str__(self):
        return self.display_name


class User(AbstractUser):
    """Custom user model for PetPaw application"""
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=100, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pics', default='default_profile.jpg')
    is_pet_owner = models.BooleanField(default=False)
    is_service_provider = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=15, blank=True)

    # Enhanced role system
    roles = models.ManyToManyField(UserRole, blank=True, related_name='users')
    primary_role = models.ForeignKey(UserRole, on_delete=models.SET_NULL, null=True, blank=True, related_name='primary_users')

    # User preferences
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    privacy_level = models.CharField(max_length=10, choices=[
        ('public', 'Public'),
        ('friends', 'Friends Only'),
        ('private', 'Private')
    ], default='public')

    def __str__(self):
        return self.username

    def get_absolute_url(self):
        return reverse('user-profile', kwargs={'username': self.username})

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.roles.filter(name=role_name).exists()

    def has_permission(self, permission):
        """Check if user has a specific permission through their roles"""
        return self.roles.filter(**{permission: True}).exists()

    def get_display_roles(self):
        """Get comma-separated list of role display names"""
        return ', '.join(role.display_name for role in self.roles.all())


class UserProfile(models.Model):
    """Extended profile information for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    followers = models.ManyToManyField(User, related_name='following', blank=True)
    website = models.URLField(max_length=200, blank=True)
    interests = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.user.username}'s profile"

    def get_followers_count(self):
        return self.followers.count()

    def get_following_count(self):
        return self.user.following.count()

    def get_pets_count(self):
        return self.user.pets.count()


class Address(models.Model):
    """User address model for shipping and billing"""
    ADDRESS_TYPES = (
        ('shipping', 'Shipping'),
        ('billing', 'Billing'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses')
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPES)
    street_address = models.CharField(max_length=255)
    apartment_address = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=20)
    default = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = 'Addresses'

    def __str__(self):
        return f"{self.user.username}'s {self.address_type} address"


class UserActivity(models.Model):
    """Model to track user activities for activity feed"""
    ACTIVITY_TYPES = (
        ('pet_added', 'Added a new pet'),
        ('pet_updated', 'Updated pet information'),
        ('pet_adopted', 'Pet was adopted'),
        ('review_written', 'Wrote a review'),
        ('service_booked', 'Booked a service'),
        ('order_placed', 'Placed an order'),
        ('profile_updated', 'Updated profile'),
        ('became_provider', 'Became a service provider'),
        ('service_added', 'Added a new service'),
        ('product_purchased', 'Purchased a product'),
        ('pet_followed', 'Followed a pet'),
        ('user_followed', 'Followed a user'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.CharField(max_length=255)

    # Generic foreign key to link to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    created_at = models.DateTimeField(auto_now_add=True)
    is_public = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'User Activities'

    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()}"


class Wishlist(models.Model):
    """Model for user wishlists/saved items"""
    ITEM_TYPES = (
        ('pet', 'Pet'),
        ('product', 'Product'),
        ('service', 'Service'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='wishlist_items')
    item_type = models.CharField(max_length=10, choices=ITEM_TYPES)

    # Generic foreign key to link to pets, products, or services
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    created_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, help_text="Personal notes about this item")

    class Meta:
        ordering = ['-created_at']
        unique_together = ('user', 'content_type', 'object_id')

    def __str__(self):
        return f"{self.user.username}'s wishlist - {self.content_object}"


class UserPreferences(models.Model):
    """Extended user preferences and settings"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')

    # Activity feed preferences
    show_pet_activities = models.BooleanField(default=True)
    show_service_activities = models.BooleanField(default=True)
    show_shop_activities = models.BooleanField(default=True)
    show_social_activities = models.BooleanField(default=True)

    # Notification preferences
    notify_on_pet_inquiry = models.BooleanField(default=True)
    notify_on_service_booking = models.BooleanField(default=True)
    notify_on_order_update = models.BooleanField(default=True)
    notify_on_new_follower = models.BooleanField(default=True)
    notify_on_review = models.BooleanField(default=True)

    # Privacy settings
    show_activity_feed = models.BooleanField(default=True)
    show_wishlist = models.BooleanField(default=False)
    show_pets = models.BooleanField(default=True)
    show_reviews = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.user.username}'s preferences"
