# Generated by Django 4.2.7 on 2025-05-27 09:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('regular', 'Regular User'), ('pet_owner', 'Pet Owner'), ('service_provider', 'Service Provider'), ('shop_vendor', 'Shop Vendor'), ('moderator', 'Moderator'), ('admin', 'Administrator')], max_length=20, unique=True)),
                ('display_name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('can_add_pets', models.BooleanField(default=False)),
                ('can_sell_pets', models.BooleanField(default=False)),
                ('can_provide_services', models.BooleanField(default=False)),
                ('can_sell_products', models.BooleanField(default=False)),
                ('can_moderate_content', models.BooleanField(default=False)),
                ('can_manage_users', models.BooleanField(default=False)),
                ('can_access_analytics', models.BooleanField(default=False)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='email_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='privacy_level',
            field=models.CharField(choices=[('public', 'Public'), ('friends', 'Friends Only'), ('private', 'Private')], default='public', max_length=10),
        ),
        migrations.AddField(
            model_name='user',
            name='push_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('show_pet_activities', models.BooleanField(default=True)),
                ('show_service_activities', models.BooleanField(default=True)),
                ('show_shop_activities', models.BooleanField(default=True)),
                ('show_social_activities', models.BooleanField(default=True)),
                ('notify_on_pet_inquiry', models.BooleanField(default=True)),
                ('notify_on_service_booking', models.BooleanField(default=True)),
                ('notify_on_order_update', models.BooleanField(default=True)),
                ('notify_on_new_follower', models.BooleanField(default=True)),
                ('notify_on_review', models.BooleanField(default=True)),
                ('show_activity_feed', models.BooleanField(default=True)),
                ('show_wishlist', models.BooleanField(default=False)),
                ('show_pets', models.BooleanField(default=True)),
                ('show_reviews', models.BooleanField(default=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('pet_added', 'Added a new pet'), ('pet_updated', 'Updated pet information'), ('pet_adopted', 'Pet was adopted'), ('review_written', 'Wrote a review'), ('service_booked', 'Booked a service'), ('order_placed', 'Placed an order'), ('profile_updated', 'Updated profile'), ('became_provider', 'Became a service provider'), ('service_added', 'Added a new service'), ('product_purchased', 'Purchased a product'), ('pet_followed', 'Followed a pet'), ('user_followed', 'Followed a user')], max_length=20)),
                ('description', models.CharField(max_length=255)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_public', models.BooleanField(default=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'User Activities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='primary_role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_users', to='users.userrole'),
        ),
        migrations.AddField(
            model_name='user',
            name='roles',
            field=models.ManyToManyField(blank=True, related_name='users', to='users.userrole'),
        ),
        migrations.CreateModel(
            name='Wishlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(choices=[('pet', 'Pet'), ('product', 'Product'), ('service', 'Service')], max_length=10)),
                ('object_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Personal notes about this item')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlist_items', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('user', 'content_type', 'object_id')},
            },
        ),
    ]
