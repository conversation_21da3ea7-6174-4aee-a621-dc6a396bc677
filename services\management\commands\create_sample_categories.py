from django.core.management.base import BaseCommand
from services.models import ServiceCategory


class Command(BaseCommand):
    help = 'Create sample service categories with icons'

    def handle(self, *args, **options):
        categories_data = [
            {
                'name': 'Pet Grooming',
                'description': 'Professional grooming services for your pets including bathing, haircuts, nail trimming, and more.',
                'icon_class': 'fas fa-cut',
            },
            {
                'name': 'Pet Walking',
                'description': 'Reliable dog walking services to keep your pets active and healthy.',
                'icon_class': 'fas fa-walking',
            },
            {
                'name': 'Pet Training',
                'description': 'Professional pet training services to help your pets learn good behavior and obedience.',
                'icon_class': 'fas fa-graduation-cap',
            },
            {
                'name': 'Pet Sitting',
                'description': 'Trusted pet sitting services for when you need to be away from home.',
                'icon_class': 'fas fa-home',
            },
            {
                'name': 'Veterinary Care',
                'description': 'Professional veterinary services including checkups, vaccinations, and medical care.',
                'icon_class': 'fas fa-stethoscope',
            },
            {
                'name': 'Pet Boarding',
                'description': 'Safe and comfortable boarding facilities for your pets during extended trips.',
                'icon_class': 'fas fa-bed',
            },
            {
                'name': 'Pet Photography',
                'description': 'Professional pet photography services to capture beautiful memories of your furry friends.',
                'icon_class': 'fas fa-camera',
            },
            {
                'name': 'Pet Transportation',
                'description': 'Safe and reliable transportation services for your pets to vet appointments and other locations.',
                'icon_class': 'fas fa-car',
            },
        ]

        created_count = 0
        updated_count = 0

        for category_data in categories_data:
            category, created = ServiceCategory.objects.get_or_create(
                name=category_data['name'],
                defaults={
                    'description': category_data['description'],
                    'icon_class': category_data['icon_class'],
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created category: {category.name}')
                )
            else:
                # Update existing category if needed
                if category.description != category_data['description'] or category.icon_class != category_data['icon_class']:
                    category.description = category_data['description']
                    category.icon_class = category_data['icon_class']
                    category.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'Updated category: {category.name}')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'Category already exists: {category.name}')
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} categories created, {updated_count} categories updated'
            )
        )
