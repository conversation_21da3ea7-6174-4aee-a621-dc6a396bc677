# Generated by Django 4.2.7 on 2025-05-27 12:38

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('pets', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='petmedicalrecord',
            options={'ordering': ['-record_date', '-created_at']},
        ),
        migrations.AddField(
            model_name='pet',
            name='activity_level',
            field=models.CharField(blank=True, choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High'), ('very_high', 'Very High')], max_length=10),
        ),
        migrations.AddField(
            model_name='pet',
            name='allergies',
            field=models.TextField(blank=True, help_text='Known allergies and sensitivities'),
        ),
        migrations.AddField(
            model_name='pet',
            name='current_weight',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Current weight in kg', max_digits=6, null=True),
        ),
        migrations.AddField(
            model_name='pet',
            name='emergency_contact_name',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='pet',
            name='emergency_contact_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='pet',
            name='emergency_contact_relationship',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='pet',
            name='insurance_policy_number',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='pet',
            name='insurance_provider',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='pet',
            name='is_spayed_neutered',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='pet',
            name='microchip_id',
            field=models.CharField(blank=True, help_text='Microchip identification number', max_length=50),
        ),
        migrations.AddField(
            model_name='pet',
            name='registration_number',
            field=models.CharField(blank=True, help_text='Breed registration number', max_length=100),
        ),
        migrations.AddField(
            model_name='pet',
            name='size',
            field=models.CharField(blank=True, choices=[('XS', 'Extra Small'), ('S', 'Small'), ('M', 'Medium'), ('L', 'Large'), ('XL', 'Extra Large')], max_length=2),
        ),
        migrations.AddField(
            model_name='pet',
            name='special_needs',
            field=models.TextField(blank=True, help_text='Special care requirements'),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='clinic_address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='clinic_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='cost',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Cost of treatment', max_digits=8, null=True),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='diagnosis',
            field=models.TextField(blank=True, help_text="Veterinarian's diagnosis"),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='follow_up_date',
            field=models.DateField(blank=True, help_text='Next appointment or follow-up date', null=True),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='medications',
            field=models.TextField(blank=True, help_text='Medications prescribed'),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='prescription_image',
            field=models.ImageField(blank=True, upload_to='pet_prescriptions'),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='symptoms',
            field=models.TextField(blank=True, help_text='Symptoms observed'),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='title',
            field=models.CharField(default='Medical Record', help_text='Brief title for this record', max_length=200),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='treatment',
            field=models.TextField(blank=True, help_text='Treatment provided'),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='petmedicalrecord',
            name='urgency',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('emergency', 'Emergency')], default='low', max_length=10),
        ),
        migrations.AlterField(
            model_name='petmedicalrecord',
            name='record_type',
            field=models.CharField(choices=[('vaccination', 'Vaccination'), ('checkup', 'Regular Checkup'), ('treatment', 'Treatment'), ('surgery', 'Surgery'), ('emergency', 'Emergency Visit'), ('dental', 'Dental Care'), ('grooming', 'Grooming'), ('other', 'Other')], max_length=20),
        ),
        migrations.CreateModel(
            name='Vaccination',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vaccine_type', models.CharField(choices=[('rabies', 'Rabies'), ('dhpp', 'DHPP (Distemper, Hepatitis, Parvovirus, Parainfluenza)'), ('bordetella', 'Bordetella (Kennel Cough)'), ('lyme', 'Lyme Disease'), ('fvrcp', 'FVRCP (Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia)'), ('felv', 'FeLV (Feline Leukemia)'), ('fiv', 'FIV (Feline Immunodeficiency Virus)'), ('other', 'Other')], max_length=20)),
                ('vaccine_name', models.CharField(help_text='Brand/specific name of vaccine', max_length=100)),
                ('date_administered', models.DateField()),
                ('next_due_date', models.DateField(help_text='When the next dose is due')),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('veterinarian', models.CharField(max_length=255)),
                ('clinic', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True)),
                ('certificate', models.FileField(blank=True, upload_to='vaccination_certificates')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vaccinations', to='pets.pet')),
            ],
            options={
                'ordering': ['-date_administered'],
            },
        ),
        migrations.CreateModel(
            name='PetReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('reminder_type', models.CharField(choices=[('vaccination', 'Vaccination Due'), ('checkup', 'Veterinary Checkup'), ('medication', 'Medication'), ('grooming', 'Grooming Appointment'), ('feeding', 'Feeding Time'), ('exercise', 'Exercise Time'), ('training', 'Training Session'), ('weight_check', 'Weight Check'), ('custom', 'Custom Reminder')], max_length=20)),
                ('due_date', models.DateTimeField()),
                ('frequency', models.CharField(choices=[('once', 'One Time'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], default='once', max_length=10)),
                ('is_completed', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='pets.pet')),
            ],
            options={
                'ordering': ['due_date'],
            },
        ),
        migrations.CreateModel(
            name='PetExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('category', models.CharField(choices=[('food', 'Food & Treats'), ('medical', 'Medical & Veterinary'), ('grooming', 'Grooming'), ('toys', 'Toys & Accessories'), ('training', 'Training'), ('boarding', 'Boarding & Pet Sitting'), ('insurance', 'Insurance'), ('supplies', 'Supplies & Equipment'), ('emergency', 'Emergency'), ('other', 'Other')], max_length=20)),
                ('description', models.CharField(max_length=200)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=8)),
                ('vendor', models.CharField(blank=True, help_text='Store, clinic, or service provider', max_length=200)),
                ('receipt_image', models.ImageField(blank=True, upload_to='expense_receipts')),
                ('notes', models.TextField(blank=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='pets.pet')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='PetDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('adoption', 'Adoption Papers'), ('registration', 'Registration Certificate'), ('pedigree', 'Pedigree'), ('insurance', 'Insurance Policy'), ('medical', 'Medical Records'), ('vaccination', 'Vaccination Certificate'), ('microchip', 'Microchip Information'), ('license', 'Pet License'), ('other', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('file', models.FileField(upload_to='pet_documents')),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('issuing_authority', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='pets.pet')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Milestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('milestone_type', models.CharField(choices=[('physical', 'Physical Development'), ('behavioral', 'Behavioral'), ('training', 'Training Achievement'), ('health', 'Health Milestone'), ('social', 'Social Development'), ('other', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('date_achieved', models.DateField()),
                ('age_at_milestone', models.CharField(blank=True, help_text='Age when milestone was achieved', max_length=50)),
                ('photo', models.ImageField(blank=True, upload_to='milestone_photos')),
                ('video', models.FileField(blank=True, upload_to='milestone_videos')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='pets.pet')),
            ],
            options={
                'ordering': ['-date_achieved'],
            },
        ),
        migrations.CreateModel(
            name='FeedingSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('meal_type', models.CharField(choices=[('breakfast', 'Breakfast'), ('lunch', 'Lunch'), ('dinner', 'Dinner'), ('snack', 'Snack'), ('treat', 'Treat')], max_length=20)),
                ('time', models.TimeField(help_text='Scheduled feeding time')),
                ('food_type', models.CharField(help_text='Type/brand of food', max_length=200)),
                ('amount', models.CharField(help_text='Amount to feed (e.g., 1 cup, 200g)', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feeding_schedules', to='pets.pet')),
            ],
            options={
                'ordering': ['time'],
            },
        ),
        migrations.CreateModel(
            name='FeedingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_time', models.DateTimeField()),
                ('meal_type', models.CharField(choices=[('breakfast', 'Breakfast'), ('lunch', 'Lunch'), ('dinner', 'Dinner'), ('snack', 'Snack'), ('treat', 'Treat')], max_length=20)),
                ('food_type', models.CharField(max_length=200)),
                ('amount_given', models.CharField(max_length=100)),
                ('amount_consumed', models.CharField(blank=True, help_text='How much was actually eaten', max_length=100)),
                ('notes', models.TextField(blank=True, help_text='Appetite, behavior, etc.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feeding_logs', to='pets.pet')),
            ],
            options={
                'ordering': ['-date_time'],
            },
        ),
        migrations.CreateModel(
            name='ExerciseLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('activity_type', models.CharField(choices=[('walk', 'Walk'), ('run', 'Run'), ('play', 'Play Time'), ('training', 'Training Session'), ('swimming', 'Swimming'), ('hiking', 'Hiking'), ('fetch', 'Fetch'), ('agility', 'Agility Training'), ('other', 'Other')], max_length=20)),
                ('duration_minutes', models.PositiveIntegerField(help_text='Duration in minutes')),
                ('intensity', models.CharField(choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High')], max_length=10)),
                ('distance', models.DecimalField(blank=True, decimal_places=2, help_text='Distance in km', max_digits=6, null=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('notes', models.TextField(blank=True)),
                ('weather', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exercise_logs', to='pets.pet')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='GrowthRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_recorded', models.DateField()),
                ('weight', models.DecimalField(decimal_places=2, help_text='Weight in kg', max_digits=6)),
                ('height', models.DecimalField(blank=True, decimal_places=2, help_text='Height in cm', max_digits=6, null=True)),
                ('length', models.DecimalField(blank=True, decimal_places=2, help_text='Length in cm', max_digits=6, null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional observations')),
                ('photo', models.ImageField(blank=True, upload_to='growth_photos')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='growth_records', to='pets.pet')),
            ],
            options={
                'ordering': ['-date_recorded'],
                'unique_together': {('pet', 'date_recorded')},
            },
        ),
    ]
