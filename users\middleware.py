from django.shortcuts import redirect
from django.urls import reverse
from django.contrib.auth import get_user_model

User = get_user_model()


class ProfileCompletionMiddleware:
    """
    Middleware to check if authenticated users need to complete their profile.
    Redirects users to profile completion page if they haven't completed it yet.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # URLs that should be accessible even without completed profile
        allowed_urls = [
            reverse('complete-profile'),
            reverse('account_logout'),
            '/admin/',
            '/static/',
            '/media/',
        ]
        
        # Check if user is authenticated and profile is not completed
        if (request.user.is_authenticated and 
            not request.user.profile_completed and
            not request.user.is_staff and  # Allow staff to access admin
            not any(request.path.startswith(url) for url in allowed_urls)):
            
            # Check if user has full_name and phone_number
            if not request.user.full_name or not request.user.phone_number:
                return redirect('complete-profile')
        
        response = self.get_response(request)
        return response
