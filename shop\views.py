from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, View
from django.http import JsonResponse
from django.db.models import Q, Avg
from .models import ProductCategory, Product, Review, Cart, CartItem, Order, OrderItem
from .forms import ReviewForm, CheckoutForm
from users.models import Address
from pets.models import PetCategory


class ProductListView(ListView):
    """View for listing all products"""
    model = Product
    template_name = 'shop/product_list.html'
    context_object_name = 'products'
    paginate_by = 12

    def get_queryset(self):
        queryset = super().get_queryset().filter(is_available=True)

        # Filter by category if provided
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__id=category)

        # Filter by pet category if provided
        pet_category = self.request.GET.get('pet_category')
        if pet_category:
            queryset = queryset.filter(pet_category__id=pet_category)

        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # Sort functionality
        sort_by = self.request.GET.get('sort_by', 'name')
        if sort_by == 'price_low':
            queryset = queryset.order_by('price')
        elif sort_by == 'price_high':
            queryset = queryset.order_by('-price')
        elif sort_by == 'newest':
            queryset = queryset.order_by('-created_at')
        else:
            queryset = queryset.order_by('name')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = ProductCategory.objects.all()
        context['pet_categories'] = PetCategory.objects.all()
        context['current_category'] = self.request.GET.get('category')
        context['current_pet_category'] = self.request.GET.get('pet_category')
        context['search_query'] = self.request.GET.get('search', '')
        context['sort_by'] = self.request.GET.get('sort_by', 'name')
        return context


class ProductDetailView(DetailView):
    """View for displaying product details"""
    model = Product
    template_name = 'shop/product_detail.html'
    context_object_name = 'product'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['reviews'] = self.object.reviews.all().order_by('-created_at')
        context['review_form'] = ReviewForm()
        context['avg_rating'] = self.object.reviews.aggregate(Avg('rating'))['rating__avg']
        context['related_products'] = Product.objects.filter(
            category=self.object.category
        ).exclude(id=self.object.id)[:4]

        # Check if product is in user's wishlist
        if self.request.user.is_authenticated:
            from users.utils import is_in_wishlist
            context['in_wishlist'] = is_in_wishlist(self.request.user, self.object)

        return context


@login_required
def add_review(request, pk):
    """View for adding a product review"""
    product = get_object_or_404(Product, pk=pk)

    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            # Check if user already reviewed this product
            existing_review = Review.objects.filter(user=request.user, product=product).first()
            if existing_review:
                # Update existing review
                existing_review.rating = form.cleaned_data['rating']
                existing_review.comment = form.cleaned_data['comment']
                existing_review.save()
                messages.success(request, 'Your review has been updated!')
            else:
                # Create new review
                review = form.save(commit=False)
                review.user = request.user
                review.product = product
                review.save()
                messages.success(request, 'Your review has been added!')

    return redirect('product-detail', pk=pk)


@login_required
def add_to_cart(request, pk):
    """View for adding a product to cart"""
    product = get_object_or_404(Product, pk=pk)

    # Get or create user's cart
    cart, created = Cart.objects.get_or_create(user=request.user)

    # Check if product is already in cart
    cart_item, item_created = CartItem.objects.get_or_create(cart=cart, product=product)

    if not item_created:
        # Increment quantity if already in cart
        cart_item.quantity += 1
        cart_item.save()

    messages.success(request, f'{product.name} added to your cart!')

    # Handle AJAX requests
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'cart_total': cart.get_total_items()
        })

    return redirect('product-detail', pk=pk)


@login_required
def view_cart(request):
    """View for displaying the shopping cart"""
    cart, created = Cart.objects.get_or_create(user=request.user)
    return render(request, 'shop/cart.html', {'cart': cart})


@login_required
def update_cart_item(request, pk):
    """View for updating cart item quantity"""
    cart_item = get_object_or_404(CartItem, pk=pk, cart__user=request.user)

    if request.method == 'POST':
        quantity = int(request.POST.get('quantity', 1))

        if quantity > 0:
            cart_item.quantity = quantity
            cart_item.save()
        else:
            cart_item.delete()

    # Handle AJAX requests
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        cart = cart_item.cart
        return JsonResponse({
            'success': True,
            'cart_total': cart.get_total_items(),
            'item_total': cart_item.get_total_price() if quantity > 0 else 0,
            'cart_subtotal': cart.get_total_price()
        })

    return redirect('view-cart')


@login_required
def remove_from_cart(request, pk):
    """View for removing an item from cart"""
    cart_item = get_object_or_404(CartItem, pk=pk, cart__user=request.user)
    product_name = cart_item.product.name
    cart_item.delete()

    messages.success(request, f'{product_name} removed from your cart!')
    return redirect('view-cart')


class CheckoutView(LoginRequiredMixin, View):
    """View for checkout process"""
    def get(self, request):
        cart, created = Cart.objects.get_or_create(user=request.user)

        if not cart.items.exists():
            messages.warning(request, 'Your cart is empty!')
            return redirect('view-cart')

        # Get user's addresses
        shipping_addresses = Address.objects.filter(user=request.user, address_type='shipping')
        billing_addresses = Address.objects.filter(user=request.user, address_type='billing')

        form = CheckoutForm()

        return render(request, 'shop/checkout.html', {
            'cart': cart,
            'form': form,
            'shipping_addresses': shipping_addresses,
            'billing_addresses': billing_addresses
        })

    def post(self, request):
        cart, created = Cart.objects.get_or_create(user=request.user)

        if not cart.items.exists():
            messages.warning(request, 'Your cart is empty!')
            return redirect('view-cart')

        form = CheckoutForm(request.POST)

        if form.is_valid():
            shipping_address_id = form.cleaned_data.get('shipping_address')
            billing_address_id = form.cleaned_data.get('billing_address')

            shipping_address = Address.objects.get(id=shipping_address_id, user=request.user)
            billing_address = Address.objects.get(id=billing_address_id, user=request.user)

            # Create order
            order = Order.objects.create(
                user=request.user,
                shipping_address=shipping_address,
                billing_address=billing_address,
                total_price=cart.get_total_price()
            )

            # Create order items
            for cart_item in cart.items.all():
                OrderItem.objects.create(
                    order=order,
                    product=cart_item.product,
                    quantity=cart_item.quantity,
                    price=cart_item.product.get_current_price()
                )

            # Clear cart
            cart.items.all().delete()

            messages.success(request, 'Your order has been placed successfully!')
            return redirect('order-detail', pk=order.pk)

        # If form is invalid
        shipping_addresses = Address.objects.filter(user=request.user, address_type='shipping')
        billing_addresses = Address.objects.filter(user=request.user, address_type='billing')

        return render(request, 'shop/checkout.html', {
            'cart': cart,
            'form': form,
            'shipping_addresses': shipping_addresses,
            'billing_addresses': billing_addresses
        })


@login_required
def order_list(request):
    """View for listing user's orders"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at')
    return render(request, 'shop/order_list.html', {'orders': orders})


@login_required
def order_detail(request, pk):
    """View for displaying order details"""
    order = get_object_or_404(Order, pk=pk, user=request.user)
    return render(request, 'shop/order_detail.html', {'order': order})
