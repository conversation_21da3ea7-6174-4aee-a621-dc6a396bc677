{% extends 'base.html' %}

{% block title %}Add Photo to {{ pet.name }}'s Gallery | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .photo-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .photo-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .photo-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xl);
        text-align: center;
        margin-bottom: var(--spacing-lg);
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .file-upload-container:hover {
        border-color: var(--primary);
    }
    
    .file-upload-icon {
        font-size: var(--font-3xl);
        color: var(--gray-400);
        margin-bottom: var(--spacing-base);
    }
    
    .file-upload-text {
        margin-bottom: var(--spacing-sm);
    }
    
    .file-upload-info {
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .preview-container {
        margin-top: var(--spacing-lg);
        text-align: center;
        display: none;
    }
    
    .preview-container img {
        max-width: 100%;
        max-height: 300px;
        border-radius: var(--radius-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
    
    .form-group label {
        display: block;
        font-size: var(--font-base);
        font-weight: var(--font-weight-medium);
        margin-bottom: var(--spacing-r);
    }

    .form-group input#id_caption {
        padding: var(--spacing-r);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        font-size: var(--font-base);
        transition: var(--transition-base);
        width: 100%;
    
    }

    .form-group input#id_caption:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px var(--primary-light);
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-r);
        margin-top: var(--spacing-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="photo-form-container">
        <div class="photo-form-header">
            <h1>Add Photo to {{ pet.name }}'s Gallery</h1>
            <p>Share more moments of your furry friend</p>
        </div>
        
        <div class="photo-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Upload Photo</h2>
                    
                    <div class="file-upload-container" id="image-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-image"></i>
                        </div>
                        <div class="file-upload-text">Click to upload an image</div>
                        <div class="file-upload-info">JPG, PNG or GIF • Max 5MB</div>
                        <div style="display:none;">{{ form.image }}</div>
                    </div>
                    
                    <div class="preview-container" id="image-preview-container">
                        <img id="image-preview" src="#" alt="Preview">
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Caption</h2>
                    
                    <div class="form-group">
                        <label for="{{ form.caption.id_for_label }}" class="form-label">Add a caption (optional)</label>
                        {{ form.caption }}
                    </div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Photo</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image upload preview
        const imageUploadContainer = document.getElementById('image-upload-container');
        const imageInput = document.querySelector('input[name="image"]');
        const imagePreviewContainer = document.getElementById('image-preview-container');
        const imagePreview = document.getElementById('image-preview');
        
        imageUploadContainer.addEventListener('click', function() {
            imageInput.click();
        });
        
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.style.display = 'block';
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
</script>
{% endblock %}
