{% extends 'base.html' %}

{% block title %}Conversation with {{ other_user.username }} | PetPaw{% endblock %}

{% block extra_css %}
<style>



    .conversation-container {
        display: grid;
        grid-template-columns: 380px 1fr;
        height: calc(100vh - 140px);
        min-height: 600px;
        max-height: calc(100vh - 140px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
        border-radius: 24px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 32px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        position: relative;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .conversation-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.02) 0%,
            rgba(147, 51, 234, 0.02) 50%,
            rgba(236, 72, 153, 0.02) 100%);
        pointer-events: none;
        z-index: 0;
    }

    @media (max-width: 992px) {
        .conversation-container {
            grid-template-columns: 1fr;
            height: calc(100vh - 120px);
            border-radius: 16px;
        }

        .conversation-list {
            display: none;
        }
    }

    .conversation-list {
        border-right: 1px solid rgba(0, 0, 0, 0.08);
        overflow-y: auto;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 1;
    }

    .conversation-list::-webkit-scrollbar {
        width: 6px;
    }

    .conversation-list::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
    }

    .conversation-list::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }

    .conversation-list::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

    .conversation-list-header {
        padding: var(--spacing-lg) var(--spacing-xl);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .conversation-list-title {
        font-size: var(--font-xl);
        margin: 0;
        font-weight: 700;
        background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .new-message-button {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        border: none;
        color: var(--white);
        cursor: pointer;
        font-size: var(--font-lg);
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
    }

    .new-message-button:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }

    .search-conversations {
        padding: var(--spacing-base) var(--spacing-lg);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10px);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-base) var(--spacing-lg);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 16px;
        font-size: var(--font-base);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
        transform: translateY(-1px);
    }

    .search-input::placeholder {
        color: rgba(0, 0, 0, 0.4);
        font-weight: 400;
    }

    .conversation-items {
        padding: var(--spacing-base) 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-lg);
        margin: var(--spacing-xs) var(--spacing-base);
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        border-radius: 16px;
        position: relative;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .conversation-item:hover {
        background: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        color: inherit;
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .conversation-item.active {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
        border-color: rgba(59, 130, 246, 0.2);
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
    }

    .conversation-avatar {
        width: 56px;
        height: 56px;
        border-radius: var(--radius-full);
        object-fit: cover;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.8);
        transition: transform 0.2s ease;
    }

    .conversation-item:hover .conversation-avatar {
        transform: scale(1.05);
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }

    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-status {
        position: relative;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .conversation-detail {
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 0;
        overflow: hidden;
        position: relative;
        z-index: 1;
    }

    .conversation-detail-header {
        flex: 0 0 auto;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: var(--gap-lg);
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        position: relative;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        min-height: 80px;
    }

    .conversation-detail-avatar {
        width: 52px;
        height: 52px;
        border-radius: var(--radius-full);
        object-fit: cover;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.8);
        transition: transform 0.2s ease;
    }

    .conversation-detail-avatar:hover {
        transform: scale(1.05);
    }

    .conversation-detail-info {
        flex: 1;
    }

    .conversation-detail-name {
        font-weight: 700;
        font-size: var(--font-lg);
        background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-detail-status {
        font-size: var(--font-sm);
        color: rgba(0, 0, 0, 0.6);
        font-weight: 500;
    }

    .conversation-detail-actions {
        display: flex;
        gap: var(--gap-sm);
    }

    .conversation-action-button {
        background: none;
        border: none;
        color: var(--text-light);
        cursor: pointer;
        font-size: var(--font-lg);
        transition: var(--transition-base);
    }

    .conversation-action-button:hover {
        color: var(--primary);
    }

    .chat-messages-container {
        flex: 1 1 0;
        padding: var(--spacing-2xl) var(--spacing-xl);
        overflow-y: auto;
        overflow-x: hidden;
        display: block;
        min-height: 0;
        height: 0;
        position: relative;
        z-index: 1;
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        scroll-behavior: smooth;
    }

    .chat-messages-container::-webkit-scrollbar {
        width: 8px;
    }

    .chat-messages-container::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    .chat-messages-container::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
        border-radius: 4px;
    }

    .chat-messages-container::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.5), rgba(147, 51, 234, 0.5));
    }

    .chat-messages-container::after {
        content: "";
        display: table;
        clear: both;
    }

    .message {
        display: flex;
        max-width: 75%;
        width: fit-content;
        margin-bottom: var(--spacing-lg);
        align-items: flex-end;
        position: relative;
        animation: messageSlideIn 0.3s ease-out;
        clear: both;
    }

    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .message.incoming {
        flex-direction: row;
        float: left;
        clear: left;
    }

    .message.outgoing {
        flex-direction: row-reverse;
        float: right;
        clear: right;
        margin-left: auto;
    }

    .message-avatar {
        width: 44px;
        height: 44px;
        border-radius: var(--radius-full);
        object-fit: cover;
        margin: 0 var(--spacing-base);
        flex-shrink: 0;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.8);
        transition: transform 0.2s ease;
    }

    .message-avatar:hover {
        transform: scale(1.05);
    }

    .message.incoming .message-avatar {
        margin-right: var(--spacing-base);
        margin-left: 0;
    }

    .message.outgoing .message-avatar {
        margin-left: var(--spacing-base);
        margin-right: 0;
    }

    .message-content {
        background: rgba(255, 255, 255, 0.9);
        padding: var(--spacing-base) var(--spacing-lg);
        border-radius: 20px;
        position: relative;
        max-width: 100%;
        word-wrap: break-word;
        word-break: break-word;
        overflow-wrap: break-word;
        flex: 1;
        box-sizing: border-box;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .message-content:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
    }

    .message.outgoing .message-content {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: var(--white);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .message.incoming .message-content {
        border-bottom-left-radius: 8px;
    }

    .message.outgoing .message-content {
        border-bottom-right-radius: 8px;
    }

    .message-text {
        margin-bottom: var(--spacing-sm);
        word-wrap: break-word;
        line-height: 1.5;
        font-size: var(--font-base);
    }
    .pet-inquiry-message .message-text {
        color: var(--primary) !important;
    }

    .message.outgoing .message-text {
        color: var(--white);
    }


    .message-time {
        font-size: var(--font-xs);
        color: rgba(0, 0, 0, 0.5);
        text-align: right;
        margin-top: var(--spacing-xs);
        font-weight: 500;
    }

    .message.outgoing .message-time {
        color: rgba(255, 255, 255, 0.7);
    }

    .message-form-container {
        flex: 0 0 auto;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        position: relative;
        z-index: 2;
        min-height: 80px;
    }

    .message-form {
        display: flex;
        align-items: flex-end;
        gap: var(--gap-base);
        background: rgba(255, 255, 255, 0.9);
        border-radius: 24px;
        padding: var(--spacing-sm);
        border: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .message-form:focus-within {
        border-color: var(--primary);
        box-shadow: 0 6px 25px rgba(59, 130, 246, 0.15);
        transform: translateY(-1px);
    }

    .message-input {
        flex: 1;
        padding: var(--spacing-base) var(--spacing-lg);
        border: none;
        background: transparent;
        font-size: var(--font-base);
        resize: none;
        min-height: 44px;
        max-height: 120px;
        line-height: 1.5;
        font-family: inherit;
    }

    .message-input:focus {
        outline: none;
    }

    .message-input::placeholder {
        color: rgba(0, 0, 0, 0.4);
        font-weight: 400;
    }

    .conversation-action-button {
        background: none;
        border: none;
        color: rgba(0, 0, 0, 0.5);
        cursor: pointer;
        font-size: var(--font-lg);
        transition: all 0.2s ease;
        padding: var(--spacing-sm);
        border-radius: 12px;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .conversation-action-button:hover {
        color: var(--primary);
        background: rgba(59, 130, 246, 0.1);
        transform: scale(1.05);
    }

    .message-submit {
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        color: var(--white);
        border: none;
        border-radius: 16px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        font-size: var(--font-base);
    }

    .message-submit:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }

    .message-submit:active {
        transform: translateY(0) scale(0.95);
    }

    .message-submit:disabled {
        background: linear-gradient(135deg, #9ca3af, #6b7280);
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-xs);
        padding: var(--spacing-xs) 0;
    }

    .typing-dots {
        display: flex;
        gap: 2px;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
        border-radius: var(--radius-full);
        background-color: var(--text-light);
        animation: typingAnimation 1.5s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
        animation-delay: 0s;
    }

    .typing-dot:nth-child(2) {
        animation-delay: 0.3s;
    }

    .typing-dot:nth-child(3) {
        animation-delay: 0.6s;
    }

    @keyframes typingAnimation {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-5px);
        }
        100% {
            transform: translateY(0);
        }
    }

    /* Modern Pet Inquiry Message Styles */
    .pet-inquiry-message, .pet-response-message {
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 20px;
        padding: var(--spacing-lg);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
        margin-bottom: var(--spacing-base);
        backdrop-filter: blur(15px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .pet-inquiry-message::before, .pet-response-message::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.03) 0%,
            rgba(147, 51, 234, 0.03) 50%,
            rgba(236, 72, 153, 0.03) 100%);
        pointer-events: none;
        z-index: 0;
    }

    .pet-inquiry-message > *, .pet-response-message > * {
        position: relative;
        z-index: 1;
    }

    .pet-inquiry-header, .pet-response-header {
        display: flex;
        align-items: center;
        gap: var(--gap-sm);
        margin-bottom: var(--spacing-lg);
        font-weight: 700;
        color: var(--primary);
        font-size: var(--font-base);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .pet-inquiry-header i, .pet-response-header i {
        font-size: 1.2em;
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .inquiry-status {
        margin-left: auto;
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: 12px;
        font-size: var(--font-xs);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .inquiry-status-pending {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
        color: var(--white);
    }

    .inquiry-status-interested {
        background: linear-gradient(135deg, #10b981, #059669);
        color: var(--white);
    }

    .inquiry-status-not_available, .inquiry-status-declined {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: var(--white);
    }

    .inquiry-status-sold {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: var(--white);
    }

    .pet-inquiry-details {
        display: flex;
        flex-direction: column;
        gap: var(--gap-base);
        align-items: center;
        text-align: center;
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-lg);
        background: rgba(255, 255, 255, 0.8);
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .inquiry-pet-thumb {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        border: 3px solid rgba(255, 255, 255, 0.9);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .inquiry-pet-thumb:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    }

    .inquiry-pet-info {
        width: 100%;
    }

    .inquiry-pet-info h5 {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--gray-900);
        font-size: var(--font-lg);
        font-weight: 700;
        background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .inquiry-pet-info p {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--gray-600);
        font-size: var(--font-base);
        font-weight: 500;
    }

    .inquiry-pet-info .inquiry-price {
        font-weight: 700;
        color: var(--white);
        font-size: var(--font-xl);
        background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: 12px;
        display: inline-block;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        margin-top: var(--spacing-sm);
    }

    .inquiry-response-buttons {
        display: flex;
        gap: var(--gap-base);
        margin-top: var(--spacing-lg);
        flex-wrap: wrap;
        justify-content: center;
    }

    .inquiry-response-buttons .btn {
        font-size: var(--font-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: none;
    }

    .inquiry-response-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .inquiry-response-buttons .btn-success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: var(--white);
    }

    .inquiry-response-buttons .btn-outline {
        background: rgba(255, 255, 255, 0.9);
        color: var(--gray-700);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="conversation-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>

            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>

            <div class="conversation-items">
                {% for conv in conversations %}
                    <a href="{% url 'conversation-detail' pk=conv.id %}" class="conversation-item {% if conversation.id == conv.id %}active{% endif %}">
                        {% if conv.other_user.profile_picture %}
                            <img src="{{ conv.other_user.profile_picture.url }}" alt="{{ conv.other_user.username }}" class="conversation-avatar">
                        {% else %}
                            <img src="/static/img/default-avatar.svg" alt="{{ conv.other_user.username }}" class="conversation-avatar">
                        {% endif %}

                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">{{ conv.other_user.username }}</div>
                                {% if conv.last_message %}
                                    <div class="conversation-time">{{ conv.last_message.created_at|date:"g:i A" }}</div>
                                {% endif %}
                            </div>

                            {% if conv.last_message %}
                                <div class="conversation-preview">
                                    {% if conv.last_message.sender == user %}
                                        <span>You:</span>
                                    {% endif %}
                                    {{ conv.last_message.content|truncatechars:30 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="conversation-status">
                            {% if conv.unread_count > 0 and conv.last_message.sender != user %}
                                <div class="unread-badge">{{ conv.unread_count }}</div>
                            {% endif %}
                        </div>
                    </a>
                {% endfor %}
            </div>
        </div>

        <div class="conversation-detail">
            <div class="conversation-detail-header">
                {% if other_user.profile_picture %}
                    <img src="{{ other_user.profile_picture.url }}" alt="{{ other_user.username }}" class="conversation-detail-avatar">
                {% else %}
                    <img src="/static/img/default-avatar.svg" alt="{{ other_user.username }}" class="conversation-detail-avatar">
                {% endif %}

                <div class="conversation-detail-info">
                    <div class="conversation-detail-name">{{ other_user.username }}</div>
                    <div class="conversation-detail-status">
                        {% if other_user.is_online %}
                            <span class="text-success">Online</span>
                        {% else %}
                            <span>Last seen {{ other_user.last_login|timesince }} ago</span>
                        {% endif %}
                    </div>
                </div>

                <div class="conversation-detail-actions">
                    <a href="{% url 'user-profile' username=other_user.username %}" class="conversation-action-button" title="View Profile">
                        <i class="fas fa-user"></i>
                    </a>
                    <button type="button" class="conversation-action-button" title="More Options">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <div class="chat-messages-container" id="chat-messages-container">
                {% for message in messages %}
                    <div class="message {% if message.sender == user %}outgoing{% else %}incoming{% endif %}">
                        {% if message.sender != user %}
                            {% if message.sender.profile_picture %}
                                <img src="{{ message.sender.profile_picture.url }}" alt="{{ message.sender.username }}" class="message-avatar">
                            {% else %}
                                <img src="/static/img/default-avatar.svg" alt="{{ message.sender.username }}" class="message-avatar">
                            {% endif %}
                        {% endif %}

                        <div class="message-content">
                            {% if message.message_type == 'pet_inquiry' %}
                                <!-- Pet inquiry message -->
                                <div class="pet-inquiry-message">
                                    <div class="pet-inquiry-header">
                                        <i class="fas fa-paw"></i>
                                        <span>Pet Inquiry</span>
                                        {% if message.inquiry_status %}
                                            <span class="inquiry-status inquiry-status-{{ message.inquiry_status }}">
                                                {% if message.inquiry_status == 'pending' %}Pending
                                                {% elif message.inquiry_status == 'interested' %}Owner Interested
                                                {% elif message.inquiry_status == 'not_available' %}Not Available
                                                {% elif message.inquiry_status == 'declined' %}Declined
                                                {% elif message.inquiry_status == 'sold' %}Sold
                                                {% endif %}
                                            </span>
                                        {% endif %}
                                    </div>
                                    {% if message.pet %}
                                        <div class="pet-inquiry-details">
                                            <img src="{{ message.pet.profile_picture.url }}" alt="{{ message.pet.name }}" class="inquiry-pet-thumb">
                                            <div class="inquiry-pet-info">
                                                <h5>{{ message.pet.name }}</h5>
                                                <p>{{ message.pet.breed.name|default:message.pet.category.name }}</p>
                                                {% if message.pet.is_for_adoption and message.pet.adoption_price %}
                                                    <div class="inquiry-price">${{ message.pet.adoption_price }}</div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                    <div class="message-text">{{ message.content }}</div>

                                    {% if message.sender != user and message.inquiry_status == 'pending' and user == message.pet.owner %}
                                        <!-- Response buttons for pet owner -->
                                        <div class="inquiry-response-buttons">
                                            <button type="button" class="btn btn-sm btn-success respond-btn" data-message-id="{{ message.id }}" data-response="interested">
                                                <i class="fas fa-check"></i> I'm Interested
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline respond-btn" data-message-id="{{ message.id }}" data-response="not_available">
                                                <i class="fas fa-times"></i> Not Available
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline respond-btn" data-message-id="{{ message.id }}" data-response="declined">
                                                <i class="fas fa-ban"></i> Decline
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            {% elif message.message_type == 'pet_response' %}
                                <!-- Pet inquiry response message -->
                                <div class="pet-response-message">
                                    <div class="pet-response-header">
                                        <i class="fas fa-reply"></i>
                                        <span>Response to Pet Inquiry</span>
                                        <span class="inquiry-status inquiry-status-{{ message.inquiry_status }}">
                                            {% if message.inquiry_status == 'interested' %}Owner Interested
                                            {% elif message.inquiry_status == 'not_available' %}Not Available
                                            {% elif message.inquiry_status == 'declined' %}Declined
                                            {% elif message.inquiry_status == 'sold' %}Sold
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="message-text">{{ message.content }}</div>
                                </div>

                            {% else %}
                                <div class="message-text">{{ message.content }}</div>
                                {% if message.image %}
                                    <div class="message-image">
                                        <img src="{{ message.image.url }}" alt="Message image" style="max-width: 200px; border-radius: var(--radius-base); margin-top: var(--spacing-xs);">
                                    </div>
                                {% endif %}
                            {% endif %}
                            <div class="message-time">{{ message.created_at|date:"g:i A" }}</div>
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>No messages yet. Start the conversation!</p>
                    </div>
                {% endfor %}

                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <span>{{ other_user.username }} is typing</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <div class="message-form-container">
                <form method="post" action="{% url 'conversation-detail' pk=conversation.id %}" class="message-form" id="message-form" enctype="multipart/form-data">
                    {% csrf_token %}
                    <textarea name="content" id="message-input" class="message-input" placeholder="Type a message..." rows="1" required></textarea>
                    <input type="file" name="image" id="image-input" accept="image/*" style="display: none;">
                    <button type="button" class="conversation-action-button" onclick="document.getElementById('image-input').click();" title="Attach Image">
                        <i class="fas fa-image"></i>
                    </button>
                    <button type="submit" class="message-submit" id="message-submit">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Completely disable browser notifications
if ('Notification' in window) {
    // Override the Notification constructor to prevent any notifications
    window.Notification = function() {
        console.log('Browser notification blocked');
        return {
            close: function() {},
            onclick: null,
            onclose: null,
            onerror: null,
            onshow: null
        };
    };
    window.Notification.permission = 'denied';
    window.Notification.requestPermission = function() {
        return Promise.resolve('denied');
    };
}

document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('chat-messages-container');
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const messageSubmit = document.getElementById('message-submit');
    const typingIndicator = document.getElementById('typing-indicator');

    // Auto-scroll to bottom of messages
    function scrollToBottom() {
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // Initial scroll to bottom
    scrollToBottom();

    // No browser notifications - notifications will only appear in notifications tab

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const content = formData.get('content').trim();

        if (!content) return;

        // Disable submit button
        messageSubmit.disabled = true;

        // Send message via AJAX
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Add message to UI
                const messageHtml = `
                    <div class="message outgoing">
                        <div class="message-content">
                            <div class="message-text">${data.message.content}</div>
                            ${data.message.image_url ? `<div class="message-image"><img src="${data.message.image_url}" alt="Message image" style="max-width: 200px; border-radius: var(--radius-base);"></div>` : ''}
                            <div class="message-time">${data.message.created_at}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);

                // Clear form and scroll to bottom
                messageInput.value = '';
                messageInput.style.height = 'auto';
                document.getElementById('image-input').value = '';
                scrollToBottom();
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
        })
        .finally(() => {
            messageSubmit.disabled = false;
        });
    });

    // WebSocket connection for real-time messaging
    const conversationId = {{ conversation.id }};
    const ws_scheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const ws_path = `${ws_scheme}://${window.location.host}/ws/chat/${conversationId}/`;

    // Only try WebSocket if the browser supports it
    if ('WebSocket' in window) {
        try {
            const socket = new WebSocket(ws_path);

            socket.onmessage = function(e) {
            const data = JSON.parse(e.data);

            if (data.type === 'message' && !data.is_self) {
                // Add incoming message to UI
                const messageHtml = `
                    <div class="message incoming">
                        <img src="/static/img/default-avatar.svg" alt="${data.sender_username}" class="message-avatar">
                        <div class="message-content">
                            <div class="message-text">${data.message}</div>
                            <div class="message-time">${new Date(data.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
                scrollToBottom();

                // No browser notifications - notifications are handled in the backend
            } else if (data.type === 'message' && data.is_self) {
                // Handle own messages sent from other tabs/devices
                const messageHtml = `
                    <div class="message outgoing">
                        <div class="message-content">
                            <div class="message-text">${data.message}</div>
                            <div class="message-time">${new Date(data.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
                scrollToBottom();
            } else if (data.type === 'typing') {
                // Show typing indicator
                typingIndicator.style.display = 'flex';
                setTimeout(() => {
                    typingIndicator.style.display = 'none';
                }, 3000);
            }
        };

        socket.onopen = function(e) {
            console.log('WebSocket connection established');
        };

        socket.onclose = function(e) {
            console.log('WebSocket connection closed');
            // Only try to reconnect if it wasn't a normal closure
            if (e.code !== 1000) {
                console.log('Unexpected WebSocket closure, will try to reconnect...');
                // Don't reload the page, just log the issue
                // In production, you might want to show a "Connection lost" message
            }
        };

        socket.onerror = function(e) {
            console.error('WebSocket error:', e);
        };

        // Send typing indicator
        let typingTimer;
        messageInput.addEventListener('input', function() {
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    'type': 'typing'
                }));
            }
        });

        } catch (error) {
            console.log('WebSocket connection failed:', error);
        }
    } else {
        console.log('WebSocket not supported by this browser');
    }

    // Handle pet inquiry response buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('respond-btn')) {
            const messageId = e.target.getAttribute('data-message-id');
            const responseType = e.target.getAttribute('data-response');

            // Disable all response buttons for this message
            const responseButtons = document.querySelectorAll(`[data-message-id="${messageId}"]`);
            responseButtons.forEach(btn => {
                btn.disabled = true;
                btn.textContent = 'Sending...';
            });

            // Send response
            fetch(`{% url 'respond-pet-inquiry' message_id=0 %}`.replace('0', messageId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `response_type=${responseType}&custom_message=`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the updated conversation
                    window.location.reload();
                } else {
                    alert('Error: ' + data.error);
                    // Re-enable buttons
                    responseButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.textContent = btn.innerHTML;
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
                // Re-enable buttons
                responseButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.textContent = btn.innerHTML;
                });
            });
        }
    });

    // CSRF token helper function
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
</script>
{% endblock %}
