{% extends 'base.html' %}

{% block title %}Edit Profile | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .profile-edit-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .profile-edit-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .profile-edit-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .profile-picture-preview {
        width: 150px;
        height: 150px;
        border-radius: var(--radius-full);
        object-fit: cover;
        margin-bottom: var(--spacing-base);
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-r);
        margin-top: var(--spacing-2xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="profile-edit-container">
        <div class="profile-edit-header">
            <h1>Edit Profile</h1>
            <p>Update your personal information and profile settings.</p>
        </div>
        
        <div class="profile-edit-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Personal Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ user_form.first_name.id_for_label }}" class="form-label">First Name</label>
                            {{ user_form.first_name }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ user_form.last_name.id_for_label }}" class="form-label">Last Name</label>
                            {{ user_form.last_name }}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ user_form.email.id_for_label }}" class="form-label">Email Address</label>
                        {{ user_form.email }}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ user_form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                        {{ user_form.phone_number }}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ user_form.birth_date.id_for_label }}" class="form-label">Birth Date</label>
                        {{ user_form.birth_date }}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ user_form.location.id_for_label }}" class="form-label">Location</label>
                        {{ user_form.location }}
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Profile Details</h2>
                    
                    <div class="form-group">
                        <label for="{{ user_form.bio.id_for_label }}" class="form-label">Bio</label>
                        {{ user_form.bio }}
                        <small class="form-text">Tell us a little about yourself and your pets.</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ profile_form.website.id_for_label }}" class="form-label">Website</label>
                            {{ profile_form.website }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ profile_form.interests.id_for_label }}" class="form-label">Interests</label>
                            {{ profile_form.interests }}
                            <small class="form-text">Separate interests with commas.</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Profile Picture</h2>
                    
                    <div class="form-group">
                        <img src="{{ user.profile_picture.url }}" alt="{{ user.username }}" class="profile-picture-preview" id="profile-picture-preview">
                        
                        <label for="{{ user_form.profile_picture.id_for_label }}" class="form-label">Upload New Picture</label>
                        {{ user_form.profile_picture }}
                    </div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'user-profile' username=user.username %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview profile picture before upload
        const profilePictureInput = document.getElementById('{{ user_form.profile_picture.id_for_label }}');
        const profilePicturePreview = document.getElementById('profile-picture-preview');
        
        if (profilePictureInput && profilePicturePreview) {
            profilePictureInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        profilePicturePreview.src = e.target.result;
                    }
                    
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}
