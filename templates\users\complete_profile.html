{% extends "base.html" %}
{% load i18n %}

{% block title %}Complete Your Profile | PetPaw{% endblock %}

{% block content %}
<div class="container">
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">{% trans "Complete Your Profile" %}</h1>
        <p class="auth-subtitle">{% trans "Please provide your full name and phone number to continue" %}</p>
      </div>

      <form class="auth-form" method="post">
        {% csrf_token %}

        {% if form.non_field_errors %}
        <div class="alert alert-danger">
          {% for error in form.non_field_errors %}
            {{ error }}
          {% endfor %}
        </div>
        {% endif %}

        <div class="form-group">
          <label for="{{ form.full_name.id_for_label }}" class="form-label">{% trans "Full Name" %}</label>
          {{ form.full_name }}
          {% if form.full_name.errors %}
            <ul class="errorlist">
              {% for error in form.full_name.errors %}
                <li>{{ error }}</li>
              {% endfor %}
            </ul>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.phone_number.id_for_label }}" class="form-label">{% trans "Phone Number" %}</label>
          {{ form.phone_number }}
          {% if form.phone_number.errors %}
            <ul class="errorlist">
              {% for error in form.phone_number.errors %}
                <li>{{ error }}</li>
              {% endfor %}
            </ul>
          {% endif %}
        </div>

        <button type="submit" class="btn btn-primary auth-submit">{% trans "Complete Profile" %}</button>
      </form>

      <div class="auth-footer">
        <p>{% trans "This information is required to use PetPaw services." %}</p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
