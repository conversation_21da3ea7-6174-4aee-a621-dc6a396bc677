from django.urls import path
from . import views

urlpatterns = [
    path('profile/<int:user_id>/', views.UserProfileView.as_view(), name='user-profile'),
    path('profile/edit/', views.edit_profile, name='edit-profile'),
    path('profile/complete/', views.complete_profile, name='complete-profile'),
    path('follow/<int:user_id>/', views.follow_user, name='follow-user'),
    path('addresses/', views.address_list, name='address-list'),
    path('addresses/add/', views.add_address, name='add-address'),

    # New enhanced profile features
    path('activity/', views.activity_feed, name='user-activity-feed'),
    path('wishlist/', views.wishlist_view, name='user-wishlist'),
    path('preferences/', views.user_preferences, name='user-preferences'),

    # AJAX endpoints
    path('ajax/toggle-wishlist/', views.toggle_wishlist, name='toggle-wishlist'),
]
