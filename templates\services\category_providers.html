{% extends 'base.html' %}
{% load static %}

{% block title %}{{ category.name }} Providers - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <!-- Category Header -->
    <div class="category-header">
        <div class="category-header-content">
            <div class="category-header-icon">
                {% if category.icon %}
                    <img src="{{ category.icon.url }}" alt="{{ category.name }}" class="category-header-image">
                {% elif category.icon_class %}
                    <i class="{{ category.icon_class }}"></i>
                {% else %}
                    <i class="fas fa-paw"></i>
                {% endif %}
            </div>
            <div class="category-header-info">
                <h1>{{ category.name }} Providers</h1>
                {% if category.description %}
                    <p class="category-description">{{ category.description }}</p>
                {% endif %}
                <div class="category-stats">
                    <span class="provider-count">
                        <i class="fas fa-users"></i>
                        {{ providers.count }} provider{{ providers.count|pluralize }} available
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="{% url 'service-categories' %}" class="breadcrumb-link">
                <i class="fas fa-arrow-left"></i>
                All Categories
            </a>
        </nav>
    </div>

    <!-- Providers Grid -->
    <div class="providers-section">
        {% if providers %}
            <div class="providers-grid">
                {% for provider in providers %}
                    <div class="provider-card">
                        <div class="provider-card-image">
                            {% if provider.profile_picture %}
                                <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.get_full_name|default:provider.user.username }}">
                            {% else %}
                                <div class="provider-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="provider-card-content">
                            <h3 class="provider-name">{{ provider.user.get_full_name|default:provider.user.username }}</h3>
                            
                            <div class="provider-rating">
                                <div class="rating-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= provider.rating %}
                                            <i class="fas fa-star"></i>
                                        {% elif forloop.counter <= provider.rating|add:0.5 %}
                                            <i class="fas fa-star-half-alt"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="rating-text">{{ provider.rating|floatformat:1 }} ({{ provider.reviews_count }} reviews)</span>
                            </div>
                            
                            <p class="provider-bio">{{ provider.bio|truncatewords:20 }}</p>
                            
                            <div class="provider-meta">
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ provider.experience_years }} years experience</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>${{ provider.hourly_rate }}/hour</span>
                                </div>
                            </div>
                            
                            <div class="provider-services">
                                {% for service in provider.services.all|slice:":3" %}
                                    <span class="service-tag">{{ service.name }}</span>
                                {% endfor %}
                                {% if provider.services.count > 3 %}
                                    <span class="service-tag more">+{{ provider.services.count|add:"-3" }} more</span>
                                {% endif %}
                            </div>
                            
                            <div class="provider-actions">
                                <a href="{% url 'provider-detail' provider.pk %}" class="btn btn-primary">
                                    <i class="fas fa-eye"></i>
                                    View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="pagination-container">
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a href="?page=1" class="page-link">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a href="?page={{ page_obj.previous_page_number }}" class="page-link">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a href="?page={{ num }}" class="page-link">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a href="?page={{ page_obj.next_page_number }}" class="page-link">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a href="?page={{ page_obj.paginator.num_pages }}" class="page-link">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>No Providers Available</h3>
                <p>There are currently no service providers available in the {{ category.name }} category.</p>
                <a href="{% url 'service-categories' %}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i>
                    Browse Other Categories
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.category-header {
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    color: var(--white);
    padding: var(--spacing-xl) 0;
    margin-bottom: var(--spacing-xl);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.category-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.category-header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.category-header-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.category-header-icon i {
    font-size: 2rem;
    color: var(--white);
}

.category-header-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-full);
}

.category-header-info h1 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-3xl);
    font-weight: 700;
}

.category-description {
    margin: 0 0 var(--spacing-base) 0;
    opacity: 0.9;
    font-size: var(--font-lg);
    line-height: 1.6;
}

.category-stats {
    display: flex;
    gap: var(--spacing-base);
}

.provider-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
}

.breadcrumb {
    margin-top: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.breadcrumb-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--white);
    text-decoration: none;
    font-size: var(--font-sm);
    opacity: 0.9;
    transition: var(--transition-base);
}

.breadcrumb-link:hover {
    opacity: 1;
    text-decoration: none;
}

.providers-section {
    margin-top: var(--spacing-xl);
}

.providers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.provider-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-base);
}

.provider-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.provider-card-image {
    width: 100%;
    height: 200px;
    position: relative;
    overflow: hidden;
}

.provider-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.provider-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.provider-placeholder i {
    font-size: 3rem;
    color: var(--gray-400);
}

.provider-card-content {
    padding: var(--spacing-lg);
}

.provider-name {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--gray-800);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-base);
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    color: var(--warning);
    font-size: var(--font-sm);
}

.rating-text {
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.provider-bio {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    font-size: var(--font-sm);
}

.provider-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.meta-item i {
    color: var(--primary);
}

.provider-services {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-lg);
}

.service-tag {
    background: var(--primary-light);
    color: var(--primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-xs);
    font-weight: 500;
}

.service-tag.more {
    background: var(--gray-100);
    color: var(--gray-600);
}

.provider-actions {
    text-align: center;
}

.provider-actions .btn {
    width: 100%;
    justify-content: center;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl) var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-xl);
}

.empty-state p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.pagination-container {
    margin-top: var(--spacing-xl);
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-xs);
}

.page-item {
    display: flex;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-base);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-base);
}

.page-link:hover {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
    text-decoration: none;
}

.page-item.active .page-link {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.page-item.disabled .page-link {
    color: var(--gray-400);
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .category-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-base);
    }
    
    .category-header-icon {
        width: 60px;
        height: 60px;
    }
    
    .category-header-icon i {
        font-size: 1.5rem;
    }
    
    .category-header-info h1 {
        font-size: var(--font-2xl);
    }
    
    .providers-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-base);
    }
    
    .provider-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .category-header {
        padding: var(--spacing-lg) 0;
    }
    
    .provider-card-content {
        padding: var(--spacing-base);
    }
    
    .provider-name {
        font-size: var(--font-lg);
    }
}
</style>
{% endblock %}
